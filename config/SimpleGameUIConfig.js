/**
 * 简化的游戏UI配置类
 * 删除所有冗余设计，只保留最基本的配置
 */
class SimpleGameUIConfig {
    constructor(canvasWidth, canvasHeight) {
        this.canvasWidth = canvasWidth;
        this.canvasHeight = canvasHeight;
        
        // 统一颜色配置 - 只需要修改这里
        this.colors = {
            primary: 'rgba(255, 182, 193, 0.8)',    // 主要背景色 - 桃红色
            secondary: 'rgba(255, 255, 255, 0.9)',  // 次要背景色 - 白色
            text: '#FFFFFF',                         // 文字颜色
            border: 'rgba(255, 182, 193, 0.6)'      // 边框颜色
        };
        
        // 统一尺寸配置
        this.sizes = {
            borderRadius: 15,
            padding: 10,
            fontSize: 18
        };
        
        // 基础布局配置
        this.layout = {
            statsBar: {
                height: 60,
                yOffset: 20
            },
            propBar: {
                height: 80,
                bottomOffset: 20
            },
            grid: {
                spacing: 2,
                inset: 3
            }
        };
    }
    
    // 获取统计栏配置
    getStatsBarConfig() {
        const width = this.canvasWidth * 0.9;
        const x = (this.canvasWidth - width) / 2;
        
        return {
            x: x,
            y: this.layout.statsBar.yOffset,
            width: width,
            height: this.layout.statsBar.height,
            backgroundColor: this.colors.primary,
            borderRadius: this.sizes.borderRadius,
            textColor: this.colors.text,
            fontSize: this.sizes.fontSize
        };
    }
    
    // 获取网格配置
    getGridConfig() {
        const statsBarBottom = this.layout.statsBar.yOffset + this.layout.statsBar.height + 20;
        const propBarTop = this.canvasHeight - this.layout.propBar.height - this.layout.propBar.bottomOffset - 20;
        const availableHeight = propBarTop - statsBarBottom;
        
        const gridSize = Math.min(this.canvasWidth * 0.9, availableHeight);
        const startX = (this.canvasWidth - gridSize) / 2;
        const startY = statsBarBottom + (availableHeight - gridSize) / 2;
        
        return {
            startX: startX,
            startY: startY,
            width: gridSize,
            height: gridSize,
            backgroundColor: this.colors.primary,
            borderRadius: this.sizes.borderRadius,
            cellSpacing: this.layout.grid.spacing,
            cellInset: this.layout.grid.inset,
            cellBackgroundColor: this.colors.secondary
        };
    }
    
    // 获取道具栏配置
    getPropBarConfig() {
        const width = this.canvasWidth * 0.9;
        const x = (this.canvasWidth - width) / 2;
        const y = this.canvasHeight - this.layout.propBar.height - this.layout.propBar.bottomOffset;
        
        return {
            x: x,
            y: y,
            width: width,
            height: this.layout.propBar.height,
            backgroundColor: this.colors.primary,
            borderRadius: this.sizes.borderRadius,
            borderColor: this.colors.border
        };
    }
    
    // 获取主页按钮配置
    getMainPageButtonsConfig() {
        return {
            width: 200,
            height: 50,
            spacing: 15,
            backgroundColor: this.colors.primary,
            textColor: this.colors.text,
            borderRadius: this.sizes.borderRadius,
            fontSize: this.sizes.fontSize
        };
    }
    
    // 计算主页按钮位置
    calculateMainPageButtonPositions(canvasWidth, canvasHeight) {
        const config = this.getMainPageButtonsConfig();
        const centerX = canvasWidth / 2;
        const startY = canvasHeight / 2 + 50;
        
        return [
            {
                x: centerX - config.width / 2,
                y: startY,
                width: config.width,
                height: config.height
            },
            {
                x: centerX - config.width / 2,
                y: startY + config.height + config.spacing,
                width: config.width,
                height: config.height
            },
            {
                x: centerX - config.width / 2,
                y: startY + (config.height + config.spacing) * 2,
                width: config.width,
                height: config.height
            }
        ];
    }
    
    // 获取主页背景配置
    getMainPageBackgroundConfig() {
        return {
            gradient: {
                colors: [
                    '#FF69B4',  // 热粉色
                    '#DDA0DD',  // 梅花色
                    '#E6E6FA',  // 薰衣草色
                    '#FFB6C1',  // 浅粉色
                    '#DDA0DD'   // 梅花色
                ],
                direction: 'radial'
            }
        };
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SimpleGameUIConfig;
} else if (typeof window !== 'undefined') {
    window.SimpleGameUIConfig = SimpleGameUIConfig;
}
