/**
 * 简化的游戏UI配置类
 * 删除所有冗余设计，只保留最基本的配置
 */
class SimpleGameUIConfig {
    constructor(canvasWidth, canvasHeight) {
        this.canvasWidth = canvasWidth;
        this.canvasHeight = canvasHeight;
        
        // 统一颜色配置 - 只需要修改这里
        this.colors = {
            primary: 'rgba(255, 182, 193, 0.8)',    // 主要背景色 - 桃红色
            secondary: 'rgba(255, 255, 255, 0.9)',  // 次要背景色 - 白色
            text: '#FFFFFF',                         // 文字颜色
            border: 'rgba(255, 182, 193, 0.6)'      // 边框颜色
        };
        
        // 统一尺寸配置
        this.sizes = {
            borderRadius: 15,
            padding: 10,
            fontSize: 18
        };
        
        // 基础布局配置
        this.layout = {
            statsBar: {
                height: 60,
                yOffset: 20
            },
            propBar: {
                height: 80,
                bottomOffset: 20
            },
            grid: {
                spacing: 2,
                inset: 3
            }
        };
    }
    
    // 获取统计栏配置
    getStatsBarConfig() {
        const width = this.canvasWidth * 0.9;
        const x = (this.canvasWidth - width) / 2;
        
        return {
            x: x,
            y: this.layout.statsBar.yOffset,
            width: width,
            height: this.layout.statsBar.height,
            backgroundColor: this.colors.primary,
            borderRadius: this.sizes.borderRadius,
            textColor: this.colors.text,
            fontSize: this.sizes.fontSize
        };
    }
    
    // 获取网格配置
    getGridConfig() {
        const statsBarBottom = this.layout.statsBar.yOffset + this.layout.statsBar.height + 20;
        const propBarTop = this.canvasHeight - this.layout.propBar.height - this.layout.propBar.bottomOffset - 20;
        const availableHeight = propBarTop - statsBarBottom;
        
        const gridSize = Math.min(this.canvasWidth * 0.9, availableHeight);
        const startX = (this.canvasWidth - gridSize) / 2;
        const startY = statsBarBottom + (availableHeight - gridSize) / 2;
        
        return {
            startX: startX,
            startY: startY,
            width: gridSize,
            height: gridSize,
            backgroundColor: this.colors.primary,
            borderRadius: this.sizes.borderRadius,
            cellSpacing: this.layout.grid.spacing,
            cellInset: this.layout.grid.inset,
            cellBackgroundColor: this.colors.secondary
        };
    }
    
    // 获取道具栏配置
    getPropBarConfig() {
        const width = this.canvasWidth * 0.9;
        const x = (this.canvasWidth - width) / 2;
        const y = this.canvasHeight - this.layout.propBar.height - this.layout.propBar.bottomOffset;
        
        return {
            x: x,
            y: y,
            width: width,
            height: this.layout.propBar.height,
            backgroundColor: this.colors.primary,
            borderRadius: this.sizes.borderRadius,
            borderColor: this.colors.border
        };
    }
    
    // 获取主页按钮配置
    getMainPageButtonsConfig() {
        return {
            width: 200,
            height: 50,
            spacing: 15,
            backgroundColor: this.colors.primary,
            textColor: this.colors.text,
            borderRadius: this.sizes.borderRadius,
            fontSize: this.sizes.fontSize
        };
    }
    
    // 计算主页按钮位置
    calculateMainPageButtonPositions(canvasWidth, canvasHeight) {
        const config = this.getMainPageButtonsConfig();
        const centerX = canvasWidth / 2;
        const startY = canvasHeight / 2 + 50;
        
        return [
            {
                x: centerX - config.width / 2,
                y: startY,
                width: config.width,
                height: config.height
            },
            {
                x: centerX - config.width / 2,
                y: startY + config.height + config.spacing,
                width: config.width,
                height: config.height
            },
            {
                x: centerX - config.width / 2,
                y: startY + (config.height + config.spacing) * 2,
                width: config.width,
                height: config.height
            }
        ];
    }
    
    // 获取主页背景配置
    getMainPageBackgroundConfig() {
        return {
            gradient: {
                colors: [
                    '#FF69B4',  // 热粉色
                    '#DDA0DD',  // 梅花色
                    '#E6E6FA',  // 薰衣草色
                    '#FFB6C1',  // 浅粉色
                    '#DDA0DD'   // 梅花色
                ],
                direction: 'radial'
            }
        };
    }

    // 兼容 GamePageCore 的方法 - 重写以避免递归
    getGridConfig() {
        const statsBarBottom = this.layout.statsBar.yOffset + this.layout.statsBar.height + 20;
        const propBarTop = this.canvasHeight - this.layout.propBar.height - this.layout.propBar.bottomOffset - 20;
        const availableHeight = propBarTop - statsBarBottom;

        const gridSize = Math.min(this.canvasWidth * 0.9, availableHeight);
        const startX = (this.canvasWidth - gridSize) / 2;
        const startY = statsBarBottom + (availableHeight - gridSize) / 2;

        return {
            startX: startX,
            startY: startY,
            width: gridSize,
            height: gridSize,
            cols: 8,
            rows: 8,
            blockSize: gridSize / 8,
            padding: this.layout.grid.spacing
        };
    }

    // 获取道具初始数量
    getPropInitialCount(propType) {
        const counts = {
            refresh: 3,
            bomb: 2,
            clear: 1
        };
        return counts[propType] || 0;
    }

    // 获取动画配置
    getAnimationConfig() {
        return {
            clearFallSpeed: 6
        };
    }

    // 获取图片配置
    getImageConfig() {
        return true; // 简化版本，表示有图片配置
    }

    // 获取动物图片路径
    getAnimalImagePath(type) {
        return `images/animal/${type}.png`;
    }

    // 获取额外图片路径
    getExtraImagePath(type) {
        return `images/extra/${type}.png`;
    }

    // 获取道具图片路径
    getPropImagePath(type) {
        return `images/prop/${type}.png`;
    }

    // 获取连击分数倍率
    getComboScoreMultiplier(comboCount) {
        const multipliers = [1, 1.2, 1.5, 2, 2.5, 3];
        return multipliers[Math.min(comboCount - 1, multipliers.length - 1)] || 3;
    }

    // 获取连击音效
    getComboAudio(comboCount) {
        if (comboCount >= 5) return 'good';
        if (comboCount >= 3) return 'so';
        return null;
    }

    // 获取交换分数
    getSwapScore(type1, type2) {
        const scores = {
            'normal-rocket': 120,
            'rocket-rocket': 200,
            'normal-bomb': 200,
            'rocket-bomb': 300,
            'bomb-bomb': 500
        };
        const key = `${type1}-${type2}`;
        return scores[key] || scores[`${type2}-${type1}`] || 100;
    }

    // 获取返回按钮配置
    getBackButtonConfig() {
        return {
            x: 20,
            y: 75,
            width: 100,
            height: 40
        };
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SimpleGameUIConfig;
} else if (typeof window !== 'undefined') {
    window.SimpleGameUIConfig = SimpleGameUIConfig;
}
