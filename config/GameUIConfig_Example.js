/**
 * GameUIConfig 使用示例
 * 展示如何使用新的UI配置类来管理游戏界面布局
 */

// 使用示例
function demonstrateGameUIConfig() {
    console.log('=== GameUIConfig 使用示例 ===');
    
    // 1. 创建配置实例
    const canvas = { width: 400, height: 800 };
    const uiConfig = new GameUIConfig(canvas.width, canvas.height);
    
    console.log('1. 创建配置实例完成');
    
    // 2. 获取各个UI元素的配置
    const backButtonConfig = uiConfig.getBackButtonConfig();
    const statsBarConfig = uiConfig.getStatsBarConfig();
    const gridConfig = uiConfig.getGridConfig();
    const propBarConfig = uiConfig.getPropBarConfig();
    
    console.log('2. 获取UI配置：');
    console.log('   返回按钮:', backButtonConfig);
    console.log('   统计栏:', statsBarConfig);
    console.log('   网格:', gridConfig);
    console.log('   道具栏:', propBarConfig);
    
    // 3. 动态修改配置
    console.log('3. 动态修改配置：');
    
    // 修改统计栏宽度为90%
    uiConfig.updateConfig('statsBar', 'widthPercent', 0.9);
    console.log('   统计栏宽度修改为90%');
    
    // 修改网格Y坐标
    uiConfig.updateConfig('grid', 'yOffset', 280);
    console.log('   网格Y坐标修改为280');
    
    // 修改返回按钮文字
    uiConfig.updateConfig('backButton', 'text', '退出');
    console.log('   返回按钮文字修改为"退出"');
    
    // 4. 批量更新配置
    console.log('4. 批量更新配置：');
    const batchUpdates = {
        propBar: {
            widthPercent: 0.85,
            height: 90
        },
        grid: {
            blockSize: 65
        }
    };
    
    const updateResults = uiConfig.batchUpdateConfig(batchUpdates);
    console.log('   批量更新结果:', updateResults);
    
    // 5. 获取更新后的配置
    console.log('5. 更新后的配置：');
    const updatedStatsBar = uiConfig.getStatsBarConfig();
    const updatedGrid = uiConfig.getGridConfig();
    const updatedPropBar = uiConfig.getPropBarConfig();
    const updatedBackButton = uiConfig.getBackButtonConfig();
    
    console.log('   更新后统计栏宽度:', updatedStatsBar.width);
    console.log('   更新后网格Y坐标:', updatedGrid.startY);
    console.log('   更新后道具栏宽度:', updatedPropBar.width);
    console.log('   更新后返回按钮文字:', updatedBackButton.text);
    
    // 6. 获取配置摘要
    console.log('6. 配置摘要：');
    const summary = uiConfig.getConfigSummary();
    console.log('   完整配置摘要:', summary);
    
    // 7. 画布尺寸变化处理
    console.log('7. 画布尺寸变化处理：');
    uiConfig.updateCanvasSize(480, 800);
    const newSummary = uiConfig.getConfigSummary();
    console.log('   新画布尺寸下的配置:', newSummary.canvasSize);
    
    console.log('=== 示例演示完成 ===');
}

// 实际使用场景示例
function practicalUsageExample() {
    console.log('\n=== 实际使用场景示例 ===');
    
    // 模拟游戏初始化
    class MockGameCore {
        constructor(canvas) {
            this.canvas = canvas;
            this.uiConfig = new GameUIConfig(canvas.width, canvas.height);
            this.initializeUI();
        }
        
        initializeUI() {
            // 获取网格配置
            const gridConfig = this.uiConfig.getGridConfig();
            this.gridStartX = gridConfig.startX;
            this.gridStartY = gridConfig.startY;
            this.blockSize = gridConfig.blockSize;
            
            console.log(`网格初始化: 起始位置(${this.gridStartX}, ${this.gridStartY}), 方块大小: ${this.blockSize}`);
        }
        
        // 动态调整UI布局
        adjustLayout(adjustments) {
            console.log('调整UI布局:', adjustments);
            
            // 批量更新配置
            this.uiConfig.batchUpdateConfig(adjustments);
            
            // 重新初始化UI
            this.initializeUI();
        }
    }
    
    // 模拟渲染器
    class MockRenderer {
        constructor(gameCore) {
            this.core = gameCore;
            this.uiConfig = gameCore.uiConfig;
        }
        
        renderUI() {
            // 使用配置渲染统计栏
            const statsConfig = this.uiConfig.getStatsBarConfig();
            console.log(`渲染统计栏: 位置(${statsConfig.x}, ${statsConfig.y}), 尺寸(${statsConfig.width}x${statsConfig.height})`);
            
            // 使用配置渲染返回按钮
            const backButtonConfig = this.uiConfig.getBackButtonConfig();
            console.log(`渲染返回按钮: 位置(${backButtonConfig.x}, ${backButtonConfig.y}), 文字: "${backButtonConfig.text}"`);
            
            // 使用配置渲染道具栏
            const propBarConfig = this.uiConfig.getPropBarConfig();
            console.log(`渲染道具栏: 位置(${propBarConfig.x}, ${propBarConfig.y}), 道具间距: ${propBarConfig.propSpacing}`);
        }
    }
    
    // 创建游戏实例
    const mockCanvas = { width: 400, height: 800 };
    const gameCore = new MockGameCore(mockCanvas);
    const renderer = new MockRenderer(gameCore);
    
    // 初始渲染
    console.log('\n初始渲染:');
    renderer.renderUI();
    
    // 动态调整布局
    console.log('\n动态调整布局:');
    gameCore.adjustLayout({
        statsBar: { widthPercent: 0.9 },
        grid: { yOffset: 280 },
        propBar: { widthPercent: 0.85 }
    });
    
    // 重新渲染
    console.log('\n调整后渲染:');
    renderer.renderUI();
    
    console.log('=== 实际使用场景示例完成 ===');
}

// 配置验证示例
function configValidationExample() {
    console.log('\n=== 配置验证示例 ===');
    
    const uiConfig = new GameUIConfig(400, 800);
    
    // 验证百分比转换
    console.log('百分比转换验证:');
    const statsBar = uiConfig.getStatsBarConfig();
    console.log(`统计栏配置宽度百分比: 0.8 -> 实际宽度: ${statsBar.width}px`);
    
    const propBar = uiConfig.getPropBarConfig();
    console.log(`道具栏配置宽度百分比: 0.8 -> 实际宽度: ${propBar.width}px`);
    
    // 验证位置计算
    console.log('\n位置计算验证:');
    console.log(`统计栏居中位置: x=${statsBar.x} (应该是 ${(400 - statsBar.width) / 2})`);
    console.log(`道具栏居中位置: x=${propBar.x} (应该是 ${(400 - propBar.width) / 2})`);
    
    // 验证配置一致性
    console.log('\n配置一致性验证:');
    const backButton = uiConfig.getBackButtonConfig();
    const grid = uiConfig.getGridConfig();
    
    console.log(`返回按钮Y坐标: ${backButton.y}`);
    console.log(`统计栏Y坐标: ${statsBar.y}`);
    console.log(`网格Y坐标: ${grid.startY}`);
    console.log(`道具栏Y坐标: ${propBar.y}`);
    
    console.log('=== 配置验证示例完成 ===');
}

// 如果在浏览器环境中，自动运行示例
if (typeof window !== 'undefined') {
    // 等待GameUIConfig加载完成后运行示例
    setTimeout(() => {
        if (typeof GameUIConfig !== 'undefined') {
            demonstrateGameUIConfig();
            practicalUsageExample();
            configValidationExample();
        } else {
            console.error('GameUIConfig 未加载，请先加载 GameUIConfig.js');
        }
    }, 100);
}

// 导出示例函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        demonstrateGameUIConfig,
        practicalUsageExample,
        configValidationExample
    };
}
