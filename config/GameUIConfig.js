/**
 * 游戏UI配置类
 * 统一管理游戏界面的布局参数
 * 宽度参数使用0-1之间的数值表示百分比
 */
class GameUIConfig {
    constructor(canvasWidth, canvasHeight) {
        this.canvasWidth = canvasWidth;
        this.canvasHeight = canvasHeight;
        
        // 初始化所有UI配置
        this.initializeConfig();
    }
    
    /**
     * 初始化配置参数
     */
    initializeConfig() {
        // 返回按钮配置
        this.backButton = {
            width: 100,           // 固定宽度
            height: 32,           // 固定高度
            x: 20,               // 固定X坐标
            yOffset: -115,       // 相对于UI偏移的Y坐标偏移
            opacity: 1.0,        // 透明度
            text: '返回',        // 按钮文字
            fontSize: '16px',    // 字体大小
            fontFamily: 'Arial, "Microsoft YaHei"'  // 字体
        };
        
        // 统计栏配置
        this.statsBar = {
            widthPercent: 0.8,   // 宽度百分比
            height: 120,         // 固定高度
            yOffset: 135,        // Y坐标偏移（UI偏移量）
            borderRadius: 15,    // 外边框圆角
            innerBorderRadius: 12, // 内边框圆角
            innerBorderOffset: 5   // 内边框偏移
        };
        
        // 网格配置
        this.grid = {
            widthPercent: 0.8,   // 网格宽度占屏幕宽度的80%
            yOffset: 300,        // Y坐标偏移
            rows: 10,             // 行数（可配置）
            cols: 8,             // 列数（可配置）
            padding: 2,          // 方块之间的间距
            minBlockSize: 30,    // 最小方块大小
            maxBlockSize: 80,    // 最大方块大小
            // 网格布局选项
            layouts: {
                'classic': { rows: 8, cols: 6 },    // 经典布局
                'wide': { rows: 6, cols: 8 },       // 宽屏布局
                'tall': { rows: 10, cols: 5 },      // 高屏布局
                'square': { rows: 7, cols: 7 }      // 方形布局
            },
            currentLayout: 'classic'  // 当前使用的布局
        };
        
        // 道具栏配置
        this.propBar = {
            widthPercent: 0.8,   // 宽度百分比
            height: 80,          // 固定高度
            yOffsetFromGrid: 50, // 相对于网格底部的偏移
            propSize: 50,        // 道具按钮大小
            propCount: 3,        // 道具数量
            leftPadding: 30,     // 左边距
            borderRadius: 12,    // 圆角
            innerBorderRadius: 10 // 内边框圆角
        };

        // 动画和下落配置
        this.animation = {
            // 下落速度配置（像素/帧）
            fallSpeed: 8,           // 普通下落速度
            clearFallSpeed: 6,      // 清屏后的下落速度（稍慢一些）
            bombFallSpeed: 10,      // 炸弹后的下落速度
            rocketFallSpeed: 9,     // 小火箭后的下落速度
            refreshFallSpeed: 5,    // 刷新后的下落速度

            // 其他动画配置
            eliminateAnimationDuration: 300,  // 消除动画时长(ms)
            fallAnimationDuration: 500,       // 下落动画时长(ms)
            scaleAnimationSpeed: 0.1,         // 缩放动画速度
            rotationAnimationSpeed: 0.05      // 旋转动画速度
        };

        // 连击和特殊方块生成配置
        this.combo = {
            // 连击生成特殊方块的阈值
            rocketComboThreshold: 4,    // 4连击生成小火箭
            bombComboThreshold: 5,      // 5连击生成小炸弹

            // 连击奖励配置
            comboScoreMultiplier: 1.5,  // 连击分数倍数
            maxComboMultiplier: 5.0,    // 最大连击倍数

            // 连击显示配置
            showComboText: true,        // 是否显示连击文字
            comboTextDuration: 2000,    // 连击文字显示时长(ms)

            // 特殊方块生成概率
            specialBlockChance: 0.8,    // 达到阈值时生成特殊方块的概率

            // 连击奖励倍率配置
            scoreMultipliers: {
                2: 1.2,   // 2连击：1.2倍分数
                3: 1.5,   // 3连击：1.5倍分数
                4: 2.0,   // 4连击：2.0倍分数
                5: 2.5,   // 5连击：2.5倍分数
                6: 3.0,   // 6连击：3.0倍分数
                7: 3.5,   // 7连击：3.5倍分数
                8: 4.0,   // 8连击：4.0倍分数
                9: 4.5,   // 9连击：4.5倍分数
                10: 5.0   // 10连击及以上：5.0倍分数
            },

            // 连击音效配置
            audioTriggers: {
                2: 'wa',    // 2连击播放wa音效
                4: 'good',  // 4连击播放good音效
                6: 'cat',   // 6连击播放cat音效
                8: 'good',  // 8连击播放good音效
                10: 'cat'   // 10连击播放cat音效
            }
        };

        // 特殊方块计分规则配置
        this.scoring = {
            // 基础消除分数
            normalMatch: 10,        // 普通3连消除基础分数

            // 特殊方块单独触发分数
            singleRocket: 100,      // 单个小火箭触发分数
            singleBomb: 150,        // 单个小炸弹触发分数

            // 特殊方块交换分数
            swapScores: {
                rocketRocket: 200,      // 2个小火箭交换：十字消除
                rocketBomb: 300,        // 小火箭和小炸弹交换：3列消除
                bombBomb: 500,          // 2个小炸弹交换：5x5爆炸
                normalRocket: 120,      // 普通方块和小火箭交换
                normalBomb: 200         // 普通方块和小炸弹交换：3x3爆炸
            },

            // 消除范围配置
            eliminationRanges: {
                rocketSingle: 'line',           // 单个火箭：消除一行一列
                bombSingle: { size: 3 },        // 单个炸弹：3x3范围
                rocketRocket: 'cross',          // 火箭+火箭：十字消除
                rocketBomb: { columns: 3 },     // 火箭+炸弹：3列消除
                bombBomb: { size: 5 },          // 炸弹+炸弹：5x5爆炸
                normalRocket: 'line',           // 普通+火箭：消除一行一列
                normalBomb: { size: 3 }         // 普通+炸弹：3x3爆炸
            }
        };

        // 音频资源路径配置
        this.audio = {
            // 音频文件基础路径
            basePath: 'audios/',

            // 音频文件配置
            files: {
                background: 'background.mp3',  // 背景音乐
                bomb: 'bomb.mp3',             // 炸弹音效
                cat: 'cat.mp3',               // 猫咪音效
                good: 'good.mp3',             // 好的音效
                lose: 'lose.mp3',             // 失败音效
                shua: 'shua.mp3',             // 刷新音效
                so: 'so.mp3',                 // 消除音效
                wa: 'wa.mp3',                 // 连击音效
                win: 'win.mp3'                // 胜利音效
            }
        };

        // 图片资源路径配置
        this.images = {
            // 萌宠图片基础路径
            animalBasePath: 'images/animal/',

            // 萌宠图片文件
            animals: {
                cat: 'cat.png',
                dog: 'dog.png',
                elephant: 'elephant.png',
                fox: 'fox.png',
                frog: 'frog.png',
                monkey: 'monkey.png',
                panda: 'panda.png',
                rabbit: 'rabbit.png',
                tiger: 'tiger.png'
            },

            // 特殊方块图片基础路径
            extraBasePath: 'images/extra/',

            // 特殊方块图片文件
            extras: {
                rocket: 'rocket.png',
                bomb: 'bomb.png'
            },

            // 道具图片基础路径
            propBasePath: 'images/prop/',

            // 道具图片文件
            props: {
                refresh: 'refresh.png',
                bomb: 'bomb.png',
                clear: 'clear.png'
            },

            // 按钮图片基础路径
            buttonBasePath: 'images/button/',

            // 图标图片基础路径
            iconBasePath: 'images/icon/',

            // 标题图片
            title: 'images/title.png'
        };

        // 道具卡数量配置
        this.props = {
            // 初始道具数量
            initialCounts: {
                refresh: 3,  // 刷新卡初始数量
                bomb: 2,     // 炸弹卡初始数量
                clear: 1     // 清屏卡初始数量
            },

            // 道具使用限制
            limits: {
                refresh: 999,  // 刷新卡最大数量
                bomb: 999,     // 炸弹卡最大数量
                clear: 999      // 清屏卡最大数量
            },

            // 道具获得配置
            rewards: {
                // 通关奖励
                levelComplete: {
                    refresh: 1,
                    bomb: 1,
                    clear: 0
                },
                // 高分奖励
                highScore: {
                    refresh: 0,
                    bomb: 0,
                    clear: 0
                }
            }
        };
    }
    
    /**
     * 获取返回按钮的完整配置
     */
    getBackButtonConfig() {
        return {
            x: this.backButton.x,
            y: this.statsBar.yOffset + 50 + this.backButton.yOffset,
            width: this.backButton.width,
            height: this.backButton.height,
            opacity: this.backButton.opacity,
            text: this.backButton.text,
            fontSize: this.backButton.fontSize,
            fontFamily: this.backButton.fontFamily
        };
    }
    
    /**
     * 获取统计栏的完整配置
     */
    getStatsBarConfig() {
        const width = this.canvasWidth * this.statsBar.widthPercent;
        const x = (this.canvasWidth - width) / 2;
        
        return {
            x: x,
            y: this.statsBar.yOffset,
            width: width,
            height: this.statsBar.height,
            borderRadius: this.statsBar.borderRadius,
            innerBorder: {
                x: x + this.statsBar.innerBorderOffset,
                y: this.statsBar.yOffset + this.statsBar.innerBorderOffset,
                width: width - this.statsBar.innerBorderOffset * 2,
                height: this.statsBar.height - this.statsBar.innerBorderOffset * 2,
                borderRadius: this.statsBar.innerBorderRadius
            }
        };
    }
    
    /**
     * 获取网格的完整配置
     */
    getGridConfig() {
        // 计算可用宽度（屏幕宽度的80%）
        const availableWidth = this.canvasWidth * this.grid.widthPercent;

        // 动态计算方块大小
        // 考虑方块间距：总宽度 = 列数 * 方块大小 + (列数-1) * 间距
        const totalPadding = (this.grid.cols - 1) * this.grid.padding;
        const calculatedBlockSize = (availableWidth - totalPadding) / this.grid.cols;

        // 限制方块大小在合理范围内
        const blockSize = Math.max(
            this.grid.minBlockSize,
            Math.min(this.grid.maxBlockSize, Math.floor(calculatedBlockSize))
        );

        // 重新计算实际网格宽度
        const actualGridWidth = this.grid.cols * blockSize + (this.grid.cols - 1) * this.grid.padding;
        const actualGridHeight = this.grid.rows * blockSize + (this.grid.rows - 1) * this.grid.padding;

        // 居中对齐
        const startX = (this.canvasWidth - actualGridWidth) / 2;

        return {
            x: startX,
            y: this.grid.yOffset,
            width: actualGridWidth,
            height: actualGridHeight,
            blockSize: blockSize,
            rows: this.grid.rows,
            cols: this.grid.cols,
            padding: this.grid.padding,
            // 计算网格的实际尺寸
            gridWidth: actualGridWidth,
            gridHeight: actualGridHeight,
            // 居中对齐的起始坐标
            startX: startX,
            startY: this.grid.yOffset,
            // 可用宽度信息
            availableWidth: availableWidth,
            calculatedBlockSize: calculatedBlockSize
        };
    }
    
    /**
     * 获取道具栏的完整配置
     */
    getPropBarConfig() {
        const gridConfig = this.getGridConfig();
        const width = this.canvasWidth * this.propBar.widthPercent;
        const x = (this.canvasWidth - width) / 2;
        const y = gridConfig.startY + gridConfig.gridHeight + this.propBar.yOffsetFromGrid;
        
        // 计算道具间距
        const availableWidth = width - this.propBar.leftPadding * 2;
        const propSpacing = availableWidth / this.propBar.propCount;
        
        return {
            x: x,
            y: y,
            width: width,
            height: this.propBar.height,
            borderRadius: this.propBar.borderRadius,
            innerBorderRadius: this.propBar.innerBorderRadius,
            propSize: this.propBar.propSize,
            propSpacing: propSpacing,
            leftPadding: this.propBar.leftPadding,
            // 道具按钮的起始X坐标
            propStartX: x + this.propBar.leftPadding
        };
    }
    
    /**
     * 更新画布尺寸
     */
    updateCanvasSize(width, height) {
        this.canvasWidth = width;
        this.canvasHeight = height;
    }
    
    /**
     * 获取所有配置的摘要信息
     */
    getConfigSummary() {
        return {
            canvasSize: { width: this.canvasWidth, height: this.canvasHeight },
            backButton: this.getBackButtonConfig(),
            statsBar: this.getStatsBarConfig(),
            grid: this.getGridConfig(),
            propBar: this.getPropBarConfig()
        };
    }
    
    /**
     * 动态修改配置参数
     */
    updateConfig(section, key, value) {
        if (this[section] && this[section].hasOwnProperty(key)) {
            this[section][key] = value;
            return true;
        }
        return false;
    }
    
    /**
     * 批量更新配置
     */
    batchUpdateConfig(updates) {
        const results = {};
        for (const [section, configs] of Object.entries(updates)) {
            results[section] = {};
            for (const [key, value] of Object.entries(configs)) {
                results[section][key] = this.updateConfig(section, key, value);
            }
        }
        return results;
    }

    /**
     * 切换网格布局
     */
    setGridLayout(layoutName) {
        if (this.grid.layouts[layoutName]) {
            this.grid.currentLayout = layoutName;
            this.grid.rows = this.grid.layouts[layoutName].rows;
            this.grid.cols = this.grid.layouts[layoutName].cols;
            return true;
        }
        return false;
    }

    /**
     * 获取可用的网格布局列表
     */
    getAvailableGridLayouts() {
        return Object.keys(this.grid.layouts);
    }

    /**
     * 自定义网格尺寸
     */
    setCustomGridSize(rows, cols) {
        if (rows > 0 && cols > 0 && rows <= 15 && cols <= 15) {
            this.grid.rows = rows;
            this.grid.cols = cols;
            this.grid.currentLayout = 'custom';
            return true;
        }
        return false;
    }

    /**
     * 获取动画配置
     */
    getAnimationConfig() {
        return {
            fallSpeed: this.animation.fallSpeed,
            clearFallSpeed: this.animation.clearFallSpeed,
            bombFallSpeed: this.animation.bombFallSpeed,
            rocketFallSpeed: this.animation.rocketFallSpeed,
            refreshFallSpeed: this.animation.refreshFallSpeed,
            eliminateAnimationDuration: this.animation.eliminateAnimationDuration,
            fallAnimationDuration: this.animation.fallAnimationDuration,
            scaleAnimationSpeed: this.animation.scaleAnimationSpeed,
            rotationAnimationSpeed: this.animation.rotationAnimationSpeed
        };
    }

    /**
     * 设置下落速度
     */
    setFallSpeed(type, speed) {
        const validTypes = ['fallSpeed', 'clearFallSpeed', 'bombFallSpeed', 'rocketFallSpeed', 'refreshFallSpeed'];
        if (validTypes.includes(type) && speed > 0) {
            this.animation[type] = speed;
            return true;
        }
        return false;
    }

    /**
     * 获取连击配置
     */
    getComboConfig() {
        return {
            rocketComboThreshold: this.combo.rocketComboThreshold,
            bombComboThreshold: this.combo.bombComboThreshold,
            comboScoreMultiplier: this.combo.comboScoreMultiplier,
            maxComboMultiplier: this.combo.maxComboMultiplier,
            showComboText: this.combo.showComboText,
            comboTextDuration: this.combo.comboTextDuration,
            specialBlockChance: this.combo.specialBlockChance
        };
    }

    /**
     * 设置连击阈值
     */
    setComboThreshold(type, value) {
        if (type === 'rocket' && value > 0 && value <= 10) {
            this.combo.rocketComboThreshold = value;
            return true;
        } else if (type === 'bomb' && value > 0 && value <= 10) {
            this.combo.bombComboThreshold = value;
            return true;
        }
        return false;
    }

    /**
     * 设置连击配置
     */
    setComboConfig(config) {
        if (config.rocketComboThreshold && config.rocketComboThreshold > 0) {
            this.combo.rocketComboThreshold = config.rocketComboThreshold;
        }
        if (config.bombComboThreshold && config.bombComboThreshold > 0) {
            this.combo.bombComboThreshold = config.bombComboThreshold;
        }
        if (config.comboScoreMultiplier && config.comboScoreMultiplier > 0) {
            this.combo.comboScoreMultiplier = config.comboScoreMultiplier;
        }
        if (config.maxComboMultiplier && config.maxComboMultiplier > 0) {
            this.combo.maxComboMultiplier = config.maxComboMultiplier;
        }
        if (typeof config.showComboText === 'boolean') {
            this.combo.showComboText = config.showComboText;
        }
        if (config.comboTextDuration && config.comboTextDuration > 0) {
            this.combo.comboTextDuration = config.comboTextDuration;
        }
        if (config.specialBlockChance && config.specialBlockChance >= 0 && config.specialBlockChance <= 1) {
            this.combo.specialBlockChance = config.specialBlockChance;
        }
        return true;
    }

    /**
     * 获取音频配置
     */
    getAudioConfig() {
        return {
            basePath: this.audio.basePath,
            files: { ...this.audio.files }
        };
    }

    /**
     * 获取音频文件完整路径
     */
    getAudioPath(audioName) {
        if (this.audio.files[audioName]) {
            return this.audio.basePath + this.audio.files[audioName];
        }
        console.warn(`音频文件 ${audioName} 不存在`);
        return null;
    }

    /**
     * 获取图片配置
     */
    getImageConfig() {
        return {
            animalBasePath: this.images.animalBasePath,
            animals: { ...this.images.animals },
            extraBasePath: this.images.extraBasePath,
            extras: { ...this.images.extras },
            propBasePath: this.images.propBasePath,
            props: { ...this.images.props },
            buttonBasePath: this.images.buttonBasePath,
            iconBasePath: this.images.iconBasePath,
            title: this.images.title
        };
    }

    /**
     * 获取萌宠图片完整路径
     */
    getAnimalImagePath(animalName) {
        if (this.images.animals[animalName]) {
            return this.images.animalBasePath + this.images.animals[animalName];
        }
        console.warn(`萌宠图片 ${animalName} 不存在`);
        return null;
    }

    /**
     * 获取特殊方块图片完整路径
     */
    getExtraImagePath(extraName) {
        if (this.images.extras[extraName]) {
            return this.images.extraBasePath + this.images.extras[extraName];
        }
        console.warn(`特殊方块图片 ${extraName} 不存在`);
        return null;
    }

    /**
     * 获取道具图片完整路径
     */
    getPropImagePath(propName) {
        if (this.images.props[propName]) {
            return this.images.propBasePath + this.images.props[propName];
        }
        console.warn(`道具图片 ${propName} 不存在`);
        return null;
    }

    /**
     * 获取道具配置
     */
    getPropConfig() {
        return {
            initialCounts: { ...this.props.initialCounts },
            limits: { ...this.props.limits },
            rewards: {
                levelComplete: { ...this.props.rewards.levelComplete },
                highScore: { ...this.props.rewards.highScore }
            }
        };
    }

    /**
     * 获取道具初始数量
     */
    getPropInitialCount(propName) {
        return this.props.initialCounts[propName] || 0;
    }

    /**
     * 获取道具数量限制
     */
    getPropLimit(propName) {
        return this.props.limits[propName] || 0;
    }

    /**
     * 设置道具初始数量
     */
    setPropInitialCount(propName, count) {
        if (this.props.initialCounts.hasOwnProperty(propName) && count >= 0) {
            this.props.initialCounts[propName] = count;
            return true;
        }
        return false;
    }

    /**
     * 批量设置道具初始数量
     */
    setPropInitialCounts(counts) {
        const results = {};
        for (const [propName, count] of Object.entries(counts)) {
            results[propName] = this.setPropInitialCount(propName, count);
        }
        return results;
    }
}

// 导出配置类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GameUIConfig;
} else if (typeof window !== 'undefined') {
    window.GameUIConfig = GameUIConfig;
}
