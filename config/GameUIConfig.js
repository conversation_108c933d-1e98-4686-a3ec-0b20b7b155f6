/**
 * 游戏UI配置类
 * 统一管理游戏界面的布局参数
 * 宽度参数使用0-1之间的数值表示百分比
 */
class GameUIConfig {
    constructor(canvasWidth, canvasHeight) {
        this.canvasWidth = canvasWidth;
        this.canvasHeight = canvasHeight;
        
        // 初始化所有UI配置
        this.initializeConfig();
    }
    
    /**
     * 初始化配置参数
     */
    initializeConfig() {
        // 返回按钮配置
        this.backButton = {
            width: 100,           // 固定宽度
            height: 32,           // 固定高度
            x: 20,               // 固定X坐标
            yOffset: -115,       // 相对于UI偏移的Y坐标偏移
            opacity: 1.0,        // 透明度
            text: '返回',        // 按钮文字
            fontSize: '16px',    // 字体大小
            fontFamily: 'Arial, "Microsoft YaHei"'  // 字体
        };
        
        // 统计栏配置
        this.statsBar = {
            widthPercent: 0.8,   // 宽度百分比
            height: 120,         // 固定高度
            yOffset: 135,        // Y坐标偏移（UI偏移量）
            borderRadius: 15,    // 外边框圆角
            innerBorderRadius: 12, // 内边框圆角
            innerBorderOffset: 5   // 内边框偏移
        };
        
        // 网格配置
        this.grid = {
            widthPercent: 0.8,   // 网格宽度占屏幕宽度的80%
            yOffset: 300,        // Y坐标偏移
            rows: 10,             // 行数（可配置）
            cols: 8,             // 列数（可配置）
            padding: 2,          // 方块之间的间距
            minBlockSize: 30,    // 最小方块大小
            maxBlockSize: 80,    // 最大方块大小
            // 网格布局选项
            layouts: {
                'classic': { rows: 8, cols: 6 },    // 经典布局
                'wide': { rows: 6, cols: 8 },       // 宽屏布局
                'tall': { rows: 10, cols: 5 },      // 高屏布局
                'square': { rows: 7, cols: 7 }      // 方形布局
            },
            currentLayout: 'classic'  // 当前使用的布局
        };
        
        // 道具栏配置
        this.propBar = {
            widthPercent: 0.8,   // 宽度百分比
            height: 80,          // 固定高度
            yOffsetFromGrid: 50, // 相对于网格底部的偏移
            propSize: 50,        // 道具按钮大小
            propCount: 3,        // 道具数量
            leftPadding: 30,     // 左边距
            borderRadius: 12,    // 圆角
            innerBorderRadius: 10 // 内边框圆角
        };

        // 动画和下落配置
        this.animation = {
            // 下落速度配置（像素/帧）
            fallSpeed: 8,           // 普通下落速度
            clearFallSpeed: 6,      // 清屏后的下落速度（稍慢一些）
            bombFallSpeed: 10,      // 炸弹后的下落速度
            rocketFallSpeed: 9,     // 小火箭后的下落速度
            refreshFallSpeed: 5,    // 刷新后的下落速度

            // 其他动画配置
            eliminateAnimationDuration: 300,  // 消除动画时长(ms)
            fallAnimationDuration: 500,       // 下落动画时长(ms)
            scaleAnimationSpeed: 0.1,         // 缩放动画速度
            rotationAnimationSpeed: 0.05      // 旋转动画速度
        };

        // 连击和特殊方块生成配置
        this.combo = {
            // 连击生成特殊方块的阈值
            rocketComboThreshold: 4,    // 4连击生成小火箭
            bombComboThreshold: 5,      // 5连击生成小炸弹

            // 连击奖励配置
            comboScoreMultiplier: 1.5,  // 连击分数倍数
            maxComboMultiplier: 5.0,    // 最大连击倍数

            // 连击显示配置
            showComboText: true,        // 是否显示连击文字
            comboTextDuration: 2000,    // 连击文字显示时长(ms)

            // 特殊方块生成概率
            specialBlockChance: 0.8,    // 达到阈值时生成特殊方块的概率

            // 连击奖励倍率配置
            scoreMultipliers: {
                2: 1.2,   // 2连击：1.2倍分数
                3: 1.5,   // 3连击：1.5倍分数
                4: 2.0,   // 4连击：2.0倍分数
                5: 2.5,   // 5连击：2.5倍分数
                6: 3.0,   // 6连击：3.0倍分数
                7: 3.5,   // 7连击：3.5倍分数
                8: 4.0,   // 8连击：4.0倍分数
                9: 4.5,   // 9连击：4.5倍分数
                10: 5.0   // 10连击及以上：5.0倍分数
            },

            // 连击音效配置
            audioTriggers: {
                2: 'wa',    // 2连击播放wa音效
                4: 'good',  // 4连击播放good音效
                6: 'cat',   // 6连击播放cat音效
                8: 'good',  // 8连击播放good音效
                10: 'cat'   // 10连击播放cat音效
            }
        };

        // 特殊方块计分规则配置
        this.scoring = {
            // 基础消除分数
            normalMatch: 10,        // 普通3连消除基础分数

            // 特殊方块单独触发分数
            singleRocket: 100,      // 单个小火箭触发分数
            singleBomb: 150,        // 单个小炸弹触发分数

            // 特殊方块交换分数
            swapScores: {
                rocketRocket: 200,      // 2个小火箭交换：十字消除
                rocketBomb: 300,        // 小火箭和小炸弹交换：3列消除
                bombBomb: 500,          // 2个小炸弹交换：5x5爆炸
                normalRocket: 120,      // 普通方块和小火箭交换
                normalBomb: 200         // 普通方块和小炸弹交换：3x3爆炸
            },

            // 消除范围配置
            eliminationRanges: {
                rocketSingle: 'line',           // 单个火箭：消除一行一列
                bombSingle: { size: 3 },        // 单个炸弹：3x3范围
                rocketRocket: 'cross',          // 火箭+火箭：十字消除
                rocketBomb: { columns: 3 },     // 火箭+炸弹：3列消除
                bombBomb: { size: 5 },          // 炸弹+炸弹：5x5爆炸
                normalRocket: 'line',           // 普通+火箭：消除一行一列
                normalBomb: { size: 3 }         // 普通+炸弹：3x3爆炸
            }
        };

        // 音频资源路径配置
        this.audio = {
            // 音频文件基础路径
            basePath: 'audios/',

            // 音频文件配置
            files: {
                background: 'background.mp3',  // 背景音乐
                bomb: 'bomb.mp3',             // 炸弹音效
                cat: 'cat.mp3',               // 猫咪音效
                good: 'good.mp3',             // 好的音效
                lose: 'lose.mp3',             // 失败音效
                shua: 'shua.mp3',             // 刷新音效
                so: 'so.mp3',                 // 消除音效
                wa: 'wa.mp3',                 // 连击音效
                win: 'win.mp3'                // 胜利音效
            }
        };

        // 图片资源路径配置
        this.images = {
            // 萌宠图片基础路径
            animalBasePath: 'images/animal/',

            // 萌宠图片文件
            animals: {
                cat: 'cat.png',
                dog: 'dog.png',
                elephant: 'elephant.png',
                fox: 'fox.png',
                frog: 'frog.png',
                monkey: 'monkey.png',
                panda: 'panda.png',
                rabbit: 'rabbit.png',
                tiger: 'tiger.png'
            },

            // 特殊方块图片基础路径
            extraBasePath: 'images/extra/',

            // 特殊方块图片文件
            extras: {
                rocket: 'rocket.png',
                bomb: 'bomb.png'
            },

            // 道具图片基础路径
            propBasePath: 'images/prop/',

            // 道具图片文件
            props: {
                refresh: 'refresh.png',
                bomb: 'bomb.png',
                clear: 'clear.png'
            },

            // 按钮图片基础路径
            buttonBasePath: 'images/button/',

            // 图标图片基础路径
            iconBasePath: 'images/icon/',

            // 标题图片
            title: 'images/title.jpg'
        };

        // 主页布局配置
        this.mainPage = {
            // 背景配置
            background: {
                // 渐变背景配置
                gradient: {
                    colors: [
                        '#FF69B4',  // 热粉色
                        '#DDA0DD',  // 梅花色
                        '#E6E6FA',  // 薰衣草色
                        '#FFB6C1',  // 浅粉色
                        '#DDA0DD'   // 梅花色
                    ],
                    direction: 'radial' // 'linear' 或 'radial'
                },

                // 彩虹配置
                rainbow: {
                    enabled: false,
                    colors: ['#FF0000', '#FF7F00', '#FFFF00', '#00FF00', '#0000FF', '#4B0082', '#9400D3'],
                    width: 12,          // 彩虹宽度
                    opacity: 0.8,       // 透明度
                    animationSpeed: 0.3, // 动画速度
                    count: 1,           // 彩虹数量（只要一个大彩虹）
                    x: 0.5,             // 水平位置（屏幕中央）
                    y: 0.8625,          // 垂直位置（再向下移动150像素后的位置）
                    radius: 400         // 彩虹半径（放大一倍）
                },

                // 星星配置
                stars: {
                    enabled: true,
                    count: 80,          // 星星数量（增加到80个）
                    minSize: 4,         // 最小尺寸（从2增加到4）
                    maxSize: 10,        // 最大尺寸（从6增加到10）
                    twinkleSpeed: 2,    // 闪烁速度
                    colors: ['#FFFFFF', '#FFD700', '#FF69B4', '#87CEEB', '#FFA500', '#98FB98']
                },

                // 云朵配置
                clouds: {
                    enabled: true,
                    count: 8,           // 云朵数量
                    minSize: 60,        // 最小尺寸
                    maxSize: 120,       // 最大尺寸
                    speed: 0.3,         // 飘动速度
                    opacity: 0.7,       // 透明度
                    color: '#FFFFFF'
                },

                // 流星配置
                meteors: {
                    enabled: true,
                    count: 6,           // 同时存在的流星数量（增加到6个）
                    speed: 4,           // 流星速度
                    length: 100,        // 流星尾巴长度（增加长度）
                    colors: ['#FFD700', '#FFA500', '#FF69B4', '#87CEEB'], // 多种流星颜色
                    glowColor: '#FFFFFF', // 发光颜色
                    spawnInterval: 1500 // 生成间隔(ms)（减少间隔，更频繁生成）
                },

                // 太阳配置
                sun: {
                    enabled: false,
                    x: 0.15,            // 相对于屏幕宽度的位置 (0.15 = 15% 左上角)
                    y: 0.15,            // 相对于屏幕高度的位置 (0.15 = 15%)
                    size: 80,           // 太阳大小
                    color: '#FFD700',   // 太阳颜色
                    rayColor: '#FFA500', // 光芒颜色
                    rayCount: 12,       // 光芒数量
                    rayLength: 25,      // 光芒长度
                    rotationSpeed: 0.5, // 光芒旋转速度
                    // 笑脸配置
                    face: {
                        eyeSize: 8,     // 眼睛大小
                        eyeColor: '#FF6347', // 眼睛颜色
                        mouthWidth: 30, // 嘴巴宽度
                        mouthHeight: 15, // 嘴巴高度
                        mouthColor: '#FF6347' // 嘴巴颜色
                    }
                },

                // 彩虹桥配置
                rainbowBridge: {
                    enabled: false,
                    x: 0.5,             // 相对于屏幕宽度的中心位置
                    y: 0.7,             // 相对于屏幕高度的位置
                    width: 300,         // 彩虹桥宽度
                    height: 150,        // 彩虹桥高度
                    colors: ['#FF0000', '#FF7F00', '#FFFF00', '#00FF00', '#0000FF', '#4B0082', '#9400D3'],
                    bandWidth: 8,       // 每个颜色带的宽度
                    opacity: 0.8        // 透明度
                },

                // 桥梁配置
                bridge: {
                    enabled: false,
                    x: 0.5,             // 相对于屏幕宽度的中心位置
                    y: 0.75,            // 相对于屏幕高度的位置（在彩虹桥下方）
                    width: 280,         // 桥梁宽度
                    height: 20,         // 桥梁高度
                    color: '#8B4513',   // 桥梁颜色（棕色）
                    shadowColor: '#654321', // 阴影颜色
                    // 桥墩配置
                    pillars: {
                        enabled: true,
                        count: 3,       // 桥墩数量
                        width: 12,      // 桥墩宽度
                        height: 40,     // 桥墩高度
                        color: '#696969' // 桥墩颜色（灰色）
                    }
                }
            },

            // 标题图片配置
            titleImage: {
                // 位置配置（相对于屏幕中心的偏移）
                offsetX: 0,          // 水平偏移（0为居中）
                offsetY: -100,       // 垂直偏移（负数向上，正数向下）

                // 尺寸配置
                maxWidth: 1,       // 最大宽度（相对于屏幕宽度的比例）
                maxHeight: 180,      // 最大高度（像素）

                // 动画配置
                floatAmplitude: 8,   // 浮动幅度（像素）
                floatSpeed: 0.8,     // 浮动速度
                rotationAmplitude: 0.02, // 旋转幅度（弧度）
                rotationSpeed: 0.6,  // 旋转速度
                scaleAmplitude: 0.03, // 缩放幅度
                scaleSpeed: 1.2,     // 缩放速度
                glowMin: 0.3,        // 最小发光强度
                glowMax: 0.5,        // 最大发光强度
                glowSpeed: 2.0       // 发光速度
            },

            // 副标题配置
            subtitle: {
                // 文字内容
                text: '',

                // 位置配置
                offsetX: 0,          // 水平偏移（0为居中）
                offsetY: 150,         // 相对于主标题的垂直偏移

                // 样式配置
                fontSize: 24,        // 字体大小
                fontFamily: 'bold 24px Arial, "Microsoft YaHei"',
                color: '#FFD700',    // 文字颜色

                // 阴影配置
                shadowColor: 'rgba(0, 0, 0, 0.2)',
                shadowBlur: 5,
                shadowOffsetX: 2,
                shadowOffsetY: 2,

                // 动画配置
                floatAmplitude: 2,   // 浮动幅度（像素）
                floatSpeed: 1.5,     // 浮动速度
                alphaMin: 0.8,       // 最小透明度
                alphaMax: 1.0,       // 最大透明度
                alphaSpeed: 3.0      // 透明度变化速度
            },

            // 文字标题配置（图片加载失败时的后备方案）
            textTitle: {
                // 文字内容
                text: '休闲消消消',

                // 样式配置
                fontSize: 48,
                fontFamily: 'bold 48px Arial, "Microsoft YaHei"',
                fillColor: '#FFFFFF',
                strokeColor: '#FF69B4',
                strokeWidth: 3,

                // 阴影配置
                shadowColor: 'rgba(0, 0, 0, 0.3)',
                shadowBlur: 10,
                shadowOffsetX: 3,
                shadowOffsetY: 3
            },

            // 版本信息配置
            versionInfo: {
                enabled: true,
                text: 'v 0.0.1 BEAT',         // 版本号文本
                x: 0.95,                // 相对于屏幕宽度的位置 (95% 右下角)
                y: 0.95,                // 相对于屏幕高度的位置 (95% 右下角)
                fontSize: 14,           // 字体大小
                fontFamily: '14px Arial, "Microsoft YaHei"',
                color: '#CCCCCC',       // 文字颜色
                textAlign: 'right',     // 文字对齐方式
                textBaseline: 'bottom', // 文字基线
                // 阴影配置
                shadowColor: 'rgba(0, 0, 0, 0.3)',
                shadowBlur: 2,
                shadowOffsetX: 1,
                shadowOffsetY: 1
            },

            // 主页按钮配置 - 可爱风
            buttons: {
                // 按钮基础配置
                width: 220,             // 按钮宽度（稍微增大）
                height: 65,             // 按钮高度（稍微增大）
                spacing: 25,            // 按钮间距（增大间距）

                // 按钮位置配置（相对于屏幕中心）
                startY: 0.55,           // 第一个按钮的Y位置
                centerX: 0.5,           // 按钮水平居中位置

                // 可爱风样式配置
                style: {
                    // 渐变背景
                    gradient: {
                        colors: ['#FFB6C1', '#FF69B4', '#FF1493'], // 粉色渐变
                        direction: 'vertical'
                    },

                    // 边框配置
                    border: {
                        width: 3,
                        color: '#FFFFFF',
                        innerBorder: {
                            width: 1,
                            color: '#FF69B4'
                        }
                    },

                    // 圆角配置
                    borderRadius: 35,    // 更圆润的按钮

                    // 阴影配置
                    shadow: {
                        color: 'rgba(255, 105, 180, 0.4)',
                        blur: 15,
                        offsetX: 0,
                        offsetY: 8
                    },

                    // 内发光效果
                    innerGlow: {
                        color: 'rgba(255, 255, 255, 0.3)',
                        blur: 10
                    }
                },

                // 悬停效果
                hover: {
                    gradient: {
                        colors: ['#FFC0CB', '#FF69B4', '#FF1493'],
                        direction: 'vertical'
                    },
                    scale: 1.05,         // 悬停时放大5%
                    shadow: {
                        color: 'rgba(255, 105, 180, 0.6)',
                        blur: 20,
                        offsetX: 0,
                        offsetY: 10
                    }
                },

                // 按下效果
                pressed: {
                    scale: 0.95,         // 按下时缩小5%
                    shadow: {
                        blur: 8,
                        offsetY: 4
                    }
                },

                // 文字样式配置
                text: {
                    color: '#FFFFFF',    // 文字颜色
                    fontSize: 26,        // 字体大小（稍微增大）
                    fontFamily: 'bold 26px Arial, "Microsoft YaHei"',

                    // 文字阴影
                    shadow: {
                        color: 'rgba(0, 0, 0, 0.4)',
                        blur: 3,
                        offsetX: 1,
                        offsetY: 2
                    },

                    // 文字描边
                    stroke: {
                        color: '#FF1493',
                        width: 1
                    }
                },

                // 按钮文本配置
                texts: {
                    start: '🎮 开始游戏',
                    rank: '🏆 排行榜',
                    setting: '⚙️ 设置'
                },

                // 装饰元素
                decorations: {
                    // 小星星装饰
                    stars: {
                        enabled: true,
                        count: 3,
                        colors: ['#FFD700', '#FFFFFF', '#FFA500'],
                        size: 4,
                        animation: {
                            twinkle: true,
                            speed: 2
                        }
                    },

                    // 爱心装饰
                    hearts: {
                        enabled: true,
                        count: 2,
                        colors: ['#FF69B4', '#FFB6C1'],
                        size: 6,
                        animation: {
                            float: true,
                            speed: 1
                        }
                    }
                }
            }
        };

        // 游戏界面配置
        this.gamePage = {
            // 使用与主页相同的背景配置
            background: {
                // 渐变背景（与主页相同）
                gradient: {
                    type: 'radial',     // 径向渐变
                    direction: 'radial',
                    colors: [
                        '#FFE4E6',      // 浅粉色
                        '#FFF0F5',      // 薰衣草腮红
                        '#E6E6FA',      // 薰衣草色
                        '#F0F8FF',      // 爱丽丝蓝
                        '#FFF5EE'       // 海贝色
                    ]
                },

                // 星星配置（与主页相同）
                stars: {
                    enabled: true,
                    count: 80,          // 星星数量
                    minSize: 4,         // 最小尺寸
                    maxSize: 10,        // 最大尺寸
                    twinkleSpeed: 2,    // 闪烁速度
                    colors: ['#FFFFFF', '#FFD700', '#FF69B4', '#87CEEB', '#FFA500', '#98FB98']
                },

                // 云朵配置（与主页相同）
                clouds: {
                    enabled: true,
                    count: 8,           // 云朵数量
                    minSize: 60,        // 最小尺寸
                    maxSize: 120,       // 最大尺寸
                    speed: 0.3,         // 飘动速度
                    opacity: 0.7,       // 透明度
                    color: '#FFFFFF'
                },

                // 流星配置（与主页相同）
                meteors: {
                    enabled: true,
                    count: 6,           // 同时存在的流星数量
                    speed: 4,           // 流星速度
                    length: 100,        // 流星尾巴长度
                    colors: ['#FFD700', '#FFA500', '#FF69B4', '#87CEEB'], // 多种流星颜色
                    glowColor: '#FFFFFF', // 发光颜色
                    spawnInterval: 1500 // 生成间隔(ms)
                }
            },

            // 统计栏可爱风配置
            statsBar: {
                background: {
                    type: 'gradient',
                    colors: ['#FFB6C1', '#FFC0CB', '#FFCCCB'],
                    borderRadius: 20,
                    padding: 15,
                    shadow: {
                        color: 'rgba(255, 105, 180, 0.3)',
                        blur: 10,
                        offsetX: 0,
                        offsetY: 5
                    }
                },

                // 文字样式
                text: {
                    color: '#FFFFFF',
                    fontSize: 18,
                    fontFamily: 'bold 18px Arial, "Microsoft YaHei"',
                    shadow: {
                        color: 'rgba(0, 0, 0, 0.3)',
                        blur: 2,
                        offsetX: 1,
                        offsetY: 1
                    }
                },

                // 进度条样式
                progressBar: {
                    backgroundColor: 'rgba(255, 255, 255, 0.3)',
                    fillColor: '#FF69B4',
                    borderRadius: 10,
                    height: 8,
                    border: {
                        color: '#FFFFFF',
                        width: 2
                    }
                }
            },

            // 网格可爱风配置
            grid: {
                background: {
                    color: 'rgba(255, 255, 255, 0.15)',  // 透明白色
                    borderRadius: 15,
                    padding: 10,
                    shadow: {
                        color: 'rgba(255, 182, 193, 0.4)',
                        blur: 15,
                        offsetX: 0,
                        offsetY: 8
                    }
                },

                // 格子样式
                cell: {
                    backgroundColor: 'rgba(255, 255, 255, 0.2)', // 透明白色格子
                    borderRadius: 6,
                    spacing: 3,         // 格子间距
                    inset: 1,           // 格子内缩1像素，增加间隔感
                    border: {
                        color: 'rgba(255, 255, 255, 0.3)',
                        width: 0.5
                    },

                    // 悬停效果
                    hover: {
                        backgroundColor: 'rgba(255, 255, 255, 0.3)',
                        shadow: {
                            color: 'rgba(255, 105, 180, 0.5)',
                            blur: 8
                        }
                    }
                }
            },

            // 道具栏可爱风配置
            propBar: {
                background: {
                    type: 'gradient',
                    colors: ['#FFE4E1', '#FFF0F5', '#F0F8FF'],
                    borderRadius: 25,
                    padding: 12,
                    shadow: {
                        color: 'rgba(255, 182, 193, 0.4)',
                        blur: 12,
                        offsetX: 0,
                        offsetY: 6
                    },
                    border: {
                        color: '#FFB6C1',
                        width: 2
                    }
                },

                // 道具按钮样式
                button: {
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    borderRadius: 15,
                    padding: 8,
                    border: {
                        color: '#FF69B4',
                        width: 2
                    },
                    shadow: {
                        color: 'rgba(255, 105, 180, 0.3)',
                        blur: 6,
                        offsetX: 0,
                        offsetY: 3
                    },

                    // 按下效果
                    pressed: {
                        backgroundColor: 'rgba(255, 182, 193, 0.9)',
                        shadow: {
                            blur: 3,
                            offsetY: 1
                        }
                    }
                },

                // 数量文字样式
                countText: {
                    color: '#FF1493',
                    fontSize: 14,
                    fontFamily: 'bold 14px Arial, "Microsoft YaHei"',
                    backgroundColor: '#FFFFFF',
                    borderRadius: 10,
                    padding: 4,
                    border: {
                        color: '#FF69B4',
                        width: 1
                    }
                }
            }
        };

        // 设置页面配置
        this.settingPage = {
            // 拖动条配置
            slider: {
                // 布局比例
                sliderWidthRatio: 0.8,  // 拖动条占白色背景框的80%
                valueWidthRatio: 0.2,   // 数值占白色背景框的20%

                // 样式配置
                trackHeight: 6,         // 轨道高度
                thumbSize: 20,          // 滑块大小
                trackColor: '#E0E0E0',  // 轨道颜色
                fillColor: '#FF69B4',   // 填充颜色
                thumbColor: '#FFFFFF',  // 滑块颜色
                thumbBorderColor: '#FF69B4', // 滑块边框颜色
                thumbBorderWidth: 2,    // 滑块边框宽度

                // 文字样式
                valueFont: '16px Arial, "Microsoft YaHei"',
                valueColor: '#333333',
                labelFont: '14px Arial, "Microsoft YaHei"',
                labelColor: '#666666',

                // 间距配置
                margin: 10,             // 拖动条与边框的间距
                spacing: 8              // 拖动条与数值之间的间距
            }
        };

        // 道具卡数量配置
        this.props = {
            // 初始道具数量
            initialCounts: {
                refresh: 3,  // 刷新卡初始数量
                bomb: 2,     // 炸弹卡初始数量
                clear: 1     // 清屏卡初始数量
            },

            // 道具使用限制
            limits: {
                refresh: 999,  // 刷新卡最大数量
                bomb: 999,     // 炸弹卡最大数量
                clear: 999      // 清屏卡最大数量
            },

            // 道具获得配置
            rewards: {
                // 通关奖励
                levelComplete: {
                    refresh: 1,
                    bomb: 1,
                    clear: 0
                },
                // 高分奖励
                highScore: {
                    refresh: 0,
                    bomb: 0,
                    clear: 0
                }
            }
        };
    }
    
    /**
     * 获取返回按钮的完整配置
     */
    getBackButtonConfig() {
        return {
            x: this.backButton.x,
            y: this.statsBar.yOffset + 50 + this.backButton.yOffset,
            width: this.backButton.width,
            height: this.backButton.height,
            opacity: this.backButton.opacity,
            text: this.backButton.text,
            fontSize: this.backButton.fontSize,
            fontFamily: this.backButton.fontFamily
        };
    }
    
    /**
     * 获取统计栏的完整配置
     */
    getStatsBarConfig() {
        const width = this.canvasWidth * this.statsBar.widthPercent;
        const x = (this.canvasWidth - width) / 2;
        
        return {
            x: x,
            y: this.statsBar.yOffset,
            width: width,
            height: this.statsBar.height,
            borderRadius: this.statsBar.borderRadius,
            innerBorder: {
                x: x + this.statsBar.innerBorderOffset,
                y: this.statsBar.yOffset + this.statsBar.innerBorderOffset,
                width: width - this.statsBar.innerBorderOffset * 2,
                height: this.statsBar.height - this.statsBar.innerBorderOffset * 2,
                borderRadius: this.statsBar.innerBorderRadius
            }
        };
    }
    
    /**
     * 获取网格的完整配置
     */
    getGridConfig() {
        // 计算可用宽度（屏幕宽度的80%）
        const availableWidth = this.canvasWidth * this.grid.widthPercent;

        // 动态计算方块大小
        // 考虑方块间距：总宽度 = 列数 * 方块大小 + (列数-1) * 间距
        const totalPadding = (this.grid.cols - 1) * this.grid.padding;
        const calculatedBlockSize = (availableWidth - totalPadding) / this.grid.cols;

        // 限制方块大小在合理范围内
        const blockSize = Math.max(
            this.grid.minBlockSize,
            Math.min(this.grid.maxBlockSize, Math.floor(calculatedBlockSize))
        );

        // 重新计算实际网格宽度
        const actualGridWidth = this.grid.cols * blockSize + (this.grid.cols - 1) * this.grid.padding;
        const actualGridHeight = this.grid.rows * blockSize + (this.grid.rows - 1) * this.grid.padding;

        // 居中对齐
        const startX = (this.canvasWidth - actualGridWidth) / 2;

        return {
            x: startX,
            y: this.grid.yOffset,
            width: actualGridWidth,
            height: actualGridHeight,
            blockSize: blockSize,
            rows: this.grid.rows,
            cols: this.grid.cols,
            padding: this.grid.padding,
            // 计算网格的实际尺寸
            gridWidth: actualGridWidth,
            gridHeight: actualGridHeight,
            // 居中对齐的起始坐标
            startX: startX,
            startY: this.grid.yOffset,
            // 可用宽度信息
            availableWidth: availableWidth,
            calculatedBlockSize: calculatedBlockSize
        };
    }
    
    /**
     * 获取道具栏的完整配置
     */
    getPropBarConfig() {
        const gridConfig = this.getGridConfig();
        const width = this.canvasWidth * this.propBar.widthPercent;
        const x = (this.canvasWidth - width) / 2;
        const y = gridConfig.startY + gridConfig.gridHeight + this.propBar.yOffsetFromGrid;
        
        // 计算道具间距
        const availableWidth = width - this.propBar.leftPadding * 2;
        const propSpacing = availableWidth / this.propBar.propCount;
        
        return {
            x: x,
            y: y,
            width: width,
            height: this.propBar.height,
            borderRadius: this.propBar.borderRadius,
            innerBorderRadius: this.propBar.innerBorderRadius,
            propSize: this.propBar.propSize,
            propSpacing: propSpacing,
            leftPadding: this.propBar.leftPadding,
            // 道具按钮的起始X坐标
            propStartX: x + this.propBar.leftPadding
        };
    }
    
    /**
     * 更新画布尺寸
     */
    updateCanvasSize(width, height) {
        this.canvasWidth = width;
        this.canvasHeight = height;
    }
    
    /**
     * 获取所有配置的摘要信息
     */
    getConfigSummary() {
        return {
            canvasSize: { width: this.canvasWidth, height: this.canvasHeight },
            backButton: this.getBackButtonConfig(),
            statsBar: this.getStatsBarConfig(),
            grid: this.getGridConfig(),
            propBar: this.getPropBarConfig()
        };
    }
    
    /**
     * 动态修改配置参数
     */
    updateConfig(section, key, value) {
        if (this[section] && this[section].hasOwnProperty(key)) {
            this[section][key] = value;
            return true;
        }
        return false;
    }
    
    /**
     * 批量更新配置
     */
    batchUpdateConfig(updates) {
        const results = {};
        for (const [section, configs] of Object.entries(updates)) {
            results[section] = {};
            for (const [key, value] of Object.entries(configs)) {
                results[section][key] = this.updateConfig(section, key, value);
            }
        }
        return results;
    }

    /**
     * 切换网格布局
     */
    setGridLayout(layoutName) {
        if (this.grid.layouts[layoutName]) {
            this.grid.currentLayout = layoutName;
            this.grid.rows = this.grid.layouts[layoutName].rows;
            this.grid.cols = this.grid.layouts[layoutName].cols;
            return true;
        }
        return false;
    }

    /**
     * 获取可用的网格布局列表
     */
    getAvailableGridLayouts() {
        return Object.keys(this.grid.layouts);
    }

    /**
     * 自定义网格尺寸
     */
    setCustomGridSize(rows, cols) {
        if (rows > 0 && cols > 0 && rows <= 15 && cols <= 15) {
            this.grid.rows = rows;
            this.grid.cols = cols;
            this.grid.currentLayout = 'custom';
            return true;
        }
        return false;
    }

    /**
     * 获取动画配置
     */
    getAnimationConfig() {
        return {
            fallSpeed: this.animation.fallSpeed,
            clearFallSpeed: this.animation.clearFallSpeed,
            bombFallSpeed: this.animation.bombFallSpeed,
            rocketFallSpeed: this.animation.rocketFallSpeed,
            refreshFallSpeed: this.animation.refreshFallSpeed,
            eliminateAnimationDuration: this.animation.eliminateAnimationDuration,
            fallAnimationDuration: this.animation.fallAnimationDuration,
            scaleAnimationSpeed: this.animation.scaleAnimationSpeed,
            rotationAnimationSpeed: this.animation.rotationAnimationSpeed
        };
    }

    /**
     * 设置下落速度
     */
    setFallSpeed(type, speed) {
        const validTypes = ['fallSpeed', 'clearFallSpeed', 'bombFallSpeed', 'rocketFallSpeed', 'refreshFallSpeed'];
        if (validTypes.includes(type) && speed > 0) {
            this.animation[type] = speed;
            return true;
        }
        return false;
    }

    /**
     * 获取连击配置
     */
    getComboConfig() {
        return {
            rocketComboThreshold: this.combo.rocketComboThreshold,
            bombComboThreshold: this.combo.bombComboThreshold,
            comboScoreMultiplier: this.combo.comboScoreMultiplier,
            maxComboMultiplier: this.combo.maxComboMultiplier,
            showComboText: this.combo.showComboText,
            comboTextDuration: this.combo.comboTextDuration,
            specialBlockChance: this.combo.specialBlockChance
        };
    }

    /**
     * 设置连击阈值
     */
    setComboThreshold(type, value) {
        if (type === 'rocket' && value > 0 && value <= 10) {
            this.combo.rocketComboThreshold = value;
            return true;
        } else if (type === 'bomb' && value > 0 && value <= 10) {
            this.combo.bombComboThreshold = value;
            return true;
        }
        return false;
    }

    /**
     * 设置连击配置
     */
    setComboConfig(config) {
        if (config.rocketComboThreshold && config.rocketComboThreshold > 0) {
            this.combo.rocketComboThreshold = config.rocketComboThreshold;
        }
        if (config.bombComboThreshold && config.bombComboThreshold > 0) {
            this.combo.bombComboThreshold = config.bombComboThreshold;
        }
        if (config.comboScoreMultiplier && config.comboScoreMultiplier > 0) {
            this.combo.comboScoreMultiplier = config.comboScoreMultiplier;
        }
        if (config.maxComboMultiplier && config.maxComboMultiplier > 0) {
            this.combo.maxComboMultiplier = config.maxComboMultiplier;
        }
        if (typeof config.showComboText === 'boolean') {
            this.combo.showComboText = config.showComboText;
        }
        if (config.comboTextDuration && config.comboTextDuration > 0) {
            this.combo.comboTextDuration = config.comboTextDuration;
        }
        if (config.specialBlockChance && config.specialBlockChance >= 0 && config.specialBlockChance <= 1) {
            this.combo.specialBlockChance = config.specialBlockChance;
        }
        return true;
    }

    /**
     * 获取音频配置
     */
    getAudioConfig() {
        return {
            basePath: this.audio.basePath,
            files: { ...this.audio.files }
        };
    }

    /**
     * 获取音频文件完整路径
     */
    getAudioPath(audioName) {
        if (this.audio.files[audioName]) {
            return this.audio.basePath + this.audio.files[audioName];
        }
        console.warn(`音频文件 ${audioName} 不存在`);
        return null;
    }

    /**
     * 获取图片配置
     */
    getImageConfig() {
        return {
            animalBasePath: this.images.animalBasePath,
            animals: { ...this.images.animals },
            extraBasePath: this.images.extraBasePath,
            extras: { ...this.images.extras },
            propBasePath: this.images.propBasePath,
            props: { ...this.images.props },
            buttonBasePath: this.images.buttonBasePath,
            iconBasePath: this.images.iconBasePath,
            title: this.images.title
        };
    }

    /**
     * 获取萌宠图片完整路径
     */
    getAnimalImagePath(animalName) {
        if (this.images.animals[animalName]) {
            return this.images.animalBasePath + this.images.animals[animalName];
        }
        console.warn(`萌宠图片 ${animalName} 不存在`);
        return null;
    }

    /**
     * 获取特殊方块图片完整路径
     */
    getExtraImagePath(extraName) {
        if (this.images.extras[extraName]) {
            return this.images.extraBasePath + this.images.extras[extraName];
        }
        console.warn(`特殊方块图片 ${extraName} 不存在`);
        return null;
    }

    /**
     * 获取道具图片完整路径
     */
    getPropImagePath(propName) {
        if (this.images.props[propName]) {
            return this.images.propBasePath + this.images.props[propName];
        }
        console.warn(`道具图片 ${propName} 不存在`);
        return null;
    }

    /**
     * 获取道具配置
     */
    getPropConfig() {
        return {
            initialCounts: { ...this.props.initialCounts },
            limits: { ...this.props.limits },
            rewards: {
                levelComplete: { ...this.props.rewards.levelComplete },
                highScore: { ...this.props.rewards.highScore }
            }
        };
    }

    /**
     * 获取道具初始数量
     */
    getPropInitialCount(propName) {
        return this.props.initialCounts[propName] || 0;
    }

    /**
     * 获取道具数量限制
     */
    getPropLimit(propName) {
        return this.props.limits[propName] || 0;
    }

    /**
     * 设置道具初始数量
     */
    setPropInitialCount(propName, count) {
        if (this.props.initialCounts.hasOwnProperty(propName) && count >= 0) {
            this.props.initialCounts[propName] = count;
            return true;
        }
        return false;
    }

    /**
     * 批量设置道具初始数量
     */
    setPropInitialCounts(counts) {
        const results = {};
        for (const [propName, count] of Object.entries(counts)) {
            results[propName] = this.setPropInitialCount(propName, count);
        }
        return results;
    }

    /**
     * 获取连击配置
     */
    getComboConfig() {
        return {
            rocketComboThreshold: this.combo.rocketComboThreshold,
            bombComboThreshold: this.combo.bombComboThreshold,
            comboScoreMultiplier: this.combo.comboScoreMultiplier,
            maxComboMultiplier: this.combo.maxComboMultiplier,
            showComboText: this.combo.showComboText,
            comboTextDuration: this.combo.comboTextDuration,
            specialBlockChance: this.combo.specialBlockChance,
            scoreMultipliers: { ...this.combo.scoreMultipliers },
            audioTriggers: { ...this.combo.audioTriggers }
        };
    }

    /**
     * 获取连击分数倍率
     */
    getComboScoreMultiplier(comboCount) {
        if (comboCount >= 10) {
            return this.combo.scoreMultipliers[10] || 5.0;
        }
        return this.combo.scoreMultipliers[comboCount] || 1.0;
    }

    /**
     * 获取连击音效
     */
    getComboAudio(comboCount) {
        // 找到最接近的音效触发点
        const triggers = Object.keys(this.combo.audioTriggers)
            .map(Number)
            .sort((a, b) => a - b);

        for (let i = triggers.length - 1; i >= 0; i--) {
            if (comboCount >= triggers[i]) {
                return this.combo.audioTriggers[triggers[i]];
            }
        }
        return null;
    }

    /**
     * 获取计分规则配置
     */
    getScoringConfig() {
        return {
            normalMatch: this.scoring.normalMatch,
            singleRocket: this.scoring.singleRocket,
            singleBomb: this.scoring.singleBomb,
            swapScores: { ...this.scoring.swapScores },
            eliminationRanges: { ...this.scoring.eliminationRanges }
        };
    }

    /**
     * 获取特殊方块交换分数
     */
    getSwapScore(type1, type2) {
        // 标准化类型名称
        const normalizeType = (type) => {
            if (type === 'normal') return 'normal';
            if (type === 'rocket' || type === 'shua') return 'rocket';
            if (type === 'bomb') return 'bomb';
            return 'normal';
        };

        const t1 = normalizeType(type1);
        const t2 = normalizeType(type2);

        // 确保顺序一致
        let key;
        if (t1 === 'normal' && t2 === 'rocket') key = 'normalRocket';
        else if (t1 === 'rocket' && t2 === 'normal') key = 'normalRocket';
        else if (t1 === 'normal' && t2 === 'bomb') key = 'normalBomb';
        else if (t1 === 'bomb' && t2 === 'normal') key = 'normalBomb';
        else if (t1 === 'rocket' && t2 === 'rocket') key = 'rocketRocket';
        else if (t1 === 'rocket' && t2 === 'bomb') key = 'rocketBomb';
        else if (t1 === 'bomb' && t2 === 'rocket') key = 'rocketBomb';
        else if (t1 === 'bomb' && t2 === 'bomb') key = 'bombBomb';
        else return 0;

        return this.scoring.swapScores[key] || 0;
    }

    /**
     * 获取单个特殊方块触发分数
     */
    getSingleSpecialScore(type) {
        if (type === 'rocket' || type === 'shua') {
            return this.scoring.singleRocket;
        } else if (type === 'bomb') {
            return this.scoring.singleBomb;
        }
        return 0;
    }

    /**
     * 获取主页配置
     */
    getMainPageConfig() {
        return {
            titleImage: { ...this.mainPage.titleImage },
            subtitle: { ...this.mainPage.subtitle },
            textTitle: { ...this.mainPage.textTitle }
        };
    }

    /**
     * 获取主页标题图片配置
     */
    getTitleImageConfig() {
        return { ...this.mainPage.titleImage };
    }

    /**
     * 获取主页副标题配置
     */
    getSubtitleConfig() {
        return { ...this.mainPage.subtitle };
    }

    /**
     * 获取主页文字标题配置
     */
    getTextTitleConfig() {
        return { ...this.mainPage.textTitle };
    }

    /**
     * 计算标题图片的实际位置
     */
    calculateTitleImagePosition(canvasWidth, canvasHeight) {
        const config = this.mainPage.titleImage;
        const baseY = canvasHeight / 3; // 基础位置

        return {
            x: canvasWidth / 2 + config.offsetX,
            y: baseY + config.offsetY
        };
    }

    /**
     * 计算副标题的实际位置
     */
    calculateSubtitlePosition(canvasWidth, canvasHeight) {
        const titlePos = this.calculateTitleImagePosition(canvasWidth, canvasHeight);
        const config = this.mainPage.subtitle;

        return {
            x: canvasWidth / 2 + config.offsetX,
            y: titlePos.y + config.offsetY
        };
    }

    /**
     * 计算标题图片的实际尺寸
     */
    calculateTitleImageSize(canvasWidth, imageWidth, imageHeight) {
        const config = this.mainPage.titleImage;
        const maxWidth = canvasWidth * config.maxWidth;
        const maxHeight = config.maxHeight;

        let finalWidth = imageWidth;
        let finalHeight = imageHeight;

        // 按比例缩放
        if (finalWidth > maxWidth) {
            const ratio = maxWidth / finalWidth;
            finalWidth = maxWidth;
            finalHeight = finalHeight * ratio;
        }

        if (finalHeight > maxHeight) {
            const ratio = maxHeight / finalHeight;
            finalHeight = maxHeight;
            finalWidth = finalWidth * ratio;
        }

        return {
            width: finalWidth,
            height: finalHeight
        };
    }

    /**
     * 设置主页标题图片位置
     */
    setTitleImagePosition(offsetX, offsetY) {
        this.mainPage.titleImage.offsetX = offsetX;
        this.mainPage.titleImage.offsetY = offsetY;
    }

    /**
     * 设置主页副标题位置
     */
    setSubtitlePosition(offsetX, offsetY) {
        this.mainPage.subtitle.offsetX = offsetX;
        this.mainPage.subtitle.offsetY = offsetY;
    }

    /**
     * 设置主页标题图片尺寸
     */
    setTitleImageSize(maxWidth, maxHeight) {
        this.mainPage.titleImage.maxWidth = maxWidth;
        this.mainPage.titleImage.maxHeight = maxHeight;
    }

    /**
     * 获取主页背景配置
     */
    getMainPageBackgroundConfig() {
        if (!this.mainPage || !this.mainPage.background) {
            console.warn('主页背景配置不存在');
            return null;
        }

        return {
            gradient: this.mainPage.background.gradient ? { ...this.mainPage.background.gradient } : null,
            rainbow: this.mainPage.background.rainbow ? { ...this.mainPage.background.rainbow } : null,
            stars: this.mainPage.background.stars ? { ...this.mainPage.background.stars } : null,
            clouds: this.mainPage.background.clouds ? { ...this.mainPage.background.clouds } : null,
            meteors: this.mainPage.background.meteors ? { ...this.mainPage.background.meteors } : null,
            sun: this.mainPage.background.sun ? { ...this.mainPage.background.sun } : null,
            rainbowBridge: this.mainPage.background.rainbowBridge ? { ...this.mainPage.background.rainbowBridge } : null,
            bridge: this.mainPage.background.bridge ? { ...this.mainPage.background.bridge } : null
        };
    }

    /**
     * 获取主页版本信息配置
     */
    getVersionInfoConfig() {
        if (!this.mainPage || !this.mainPage.versionInfo) {
            return null;
        }
        return { ...this.mainPage.versionInfo };
    }

    /**
     * 获取主页按钮配置
     */
    getMainPageButtonsConfig() {
        if (!this.mainPage || !this.mainPage.buttons) {
            return null;
        }
        return { ...this.mainPage.buttons };
    }

    /**
     * 计算主页按钮位置
     */
    calculateMainPageButtonPositions(canvasWidth, canvasHeight) {
        const buttonsConfig = this.getMainPageButtonsConfig();
        if (!buttonsConfig) return [];

        const positions = [];
        const buttonNames = ['start', 'rank', 'setting'];

        for (let i = 0; i < buttonNames.length; i++) {
            const y = canvasHeight * buttonsConfig.startY + i * (buttonsConfig.height + buttonsConfig.spacing);
            positions.push({
                name: buttonNames[i],
                x: canvasWidth * buttonsConfig.centerX - buttonsConfig.width / 2,
                y: y,
                width: buttonsConfig.width,
                height: buttonsConfig.height,
                text: buttonsConfig.texts[buttonNames[i]]
            });
        }

        return positions;
    }

    /**
     * 获取设置页面配置
     */
    getSettingPageConfig() {
        return {
            slider: { ...this.settingPage.slider }
        };
    }

    /**
     * 计算拖动条布局
     */
    calculateSliderLayout(containerWidth, containerHeight) {
        const config = this.settingPage.slider;
        const availableWidth = containerWidth - (config.margin * 2);

        return {
            sliderWidth: availableWidth * config.sliderWidthRatio,
            valueWidth: availableWidth * config.valueWidthRatio,
            sliderX: config.margin,
            valueX: config.margin + (availableWidth * config.sliderWidthRatio) + config.spacing,
            centerY: containerHeight / 2
        };
    }

    /**
     * 获取游戏界面配置
     */
    getGamePageConfig() {
        if (!this.gamePage) {
            return null;
        }
        return {
            background: { ...this.gamePage.background },
            statsBar: { ...this.gamePage.statsBar },
            grid: { ...this.gamePage.grid },
            propBar: { ...this.gamePage.propBar }
        };
    }

    /**
     * 获取游戏背景配置
     */
    getGameBackgroundConfig() {
        if (!this.gamePage || !this.gamePage.background) {
            return null;
        }
        return { ...this.gamePage.background };
    }

    /**
     * 获取游戏统计栏配置
     */
    getGameStatsBarConfig() {
        if (!this.gamePage || !this.gamePage.statsBar) {
            return null;
        }
        return { ...this.gamePage.statsBar };
    }

    /**
     * 获取游戏网格配置
     */
    getGameGridConfig() {
        if (!this.gamePage || !this.gamePage.grid) {
            return null;
        }
        return { ...this.gamePage.grid };
    }

    /**
     * 获取游戏道具栏配置
     */
    getGamePropBarConfig() {
        if (!this.gamePage || !this.gamePage.propBar) {
            return null;
        }
        return { ...this.gamePage.propBar };
    }
}

// 导出配置类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GameUIConfig;
} else if (typeof window !== 'undefined') {
    window.GameUIConfig = GameUIConfig;
}
