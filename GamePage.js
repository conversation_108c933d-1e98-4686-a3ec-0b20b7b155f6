/**
 * 游戏页面 - 完整的消消消游戏实现
 * 整合所有游戏功能：核心逻辑、渲染、事件处理、动画、匹配检测等
 * 
 * @class GamePage
 * @description 完整的消消消游戏实现
 * <AUTHOR> Development Team
 * @version 3.0.0
 */
class GamePage {
    constructor(gameManager, levelConfig) {
        this.gameManager = gameManager;
        this.canvas = gameManager.canvas;
        this.ctx = gameManager.ctx;
        
        // 关卡配置
        this.levelConfig = levelConfig || {
            targetScore: 1000,
            name: '萌宠新手村',
            level: 1
        };
        
        // 游戏布局配置
        this.LAYOUT = {
            GRID_SIZE_X: 8,
            GRID_SIZE_Y: 10,
            BLOCK_SIZE: 45,
            GRID_START_Y: 310, // 向上移动20像素 (原330-20)
            UI_OFFSET_Y: 0
        };
        
        // 游戏数据
        this.score = 0;
        this.targetScore = this.levelConfig.targetScore;
        this.levelName = this.levelConfig.name;
        this.level = this.levelConfig.level;
        
        // 网格配置
        this.gridSizeX = this.LAYOUT.GRID_SIZE_X;
        this.gridSizeY = this.LAYOUT.GRID_SIZE_Y;
        this.blockSize = this.LAYOUT.BLOCK_SIZE;
        this.gridStartX = this.getGridStartX();
        this.gridStartY = this.LAYOUT.GRID_START_Y;
        
        // 游戏状态
        this.isAnimating = false;
        this.isGameOver = false;
        this.isLevelComplete = false;
        this.showExitDialog = false;
        this.showGameOverDialog = false;
        
        // 连击系统
        this.comboCount = 0;
        this.comboMultiplier = 1.0;
        this.comboTimer = 0;
        
        // 道具系统
        this.props = {
            refresh: 3,
            bomb: 2,
            clear: 1
        };
        this.propUsing = {
            type: null,
            isActive: false
        };
        
        // 萌宠类型和颜色
        this.initAnimalTypes();
        this.animalColors = {
            'cat': '#FF6B9D', 'dog': '#4ECDC4', 'elephant': '#45B7D1',
            'fox': '#96CEB4', 'frog': '#FFEAA7', 'monkey': '#DDA0DD',
            'panda': '#98D8C8', 'rabbit': '#F7DC6F', 'tiger': '#BB8FCE'
        };
        
        // 特殊方块
        this.specialTypes = { ROCKET: 'rocket', BOMB: 'bomb' };
        this.specialColors = { 'rocket': '#FF4500', 'bomb': '#8B0000' };
        
        // 计分规则
        this.scoreRules = {
            match3: 20, match4: 30, match5: 50,
            rocketCombo: 100, bombCombo: 150,
            rocketBombCombo: 300, bombBombCombo: 500
        };
        
        // 游戏网格和特效
        this.grid = [];
        this.particles = [];
        this.sparkles = [];
        this.floatingTexts = [];
        this.fallingBlocks = [];
        
        // 输入处理
        this.isDragging = false;
        this.dragStartRow = -1;
        this.dragStartCol = -1;
        this.dragEndRow = -1;
        this.dragEndCol = -1;
        this.isDraggingBomb = false;
        this.bombDragStartX = -1;
        this.bombDragStartY = -1;
        this.bombDragCurrentX = -1;
        this.bombDragCurrentY = -1;
        
        // 动画时间
        this.animationTime = 0;
        
        // 设置全局变量
        window.gameScore = this.score;
        window.gameTargetScore = this.targetScore;
        window.gameProgress = 0;
        
        console.log('GamePage 游戏页面初始化完成');
    }
    
    /**
     * 获取网格起始X坐标（80%宽度居中）
     */
    getGridStartX() {
        const gridWidth = this.LAYOUT.GRID_SIZE_X * this.LAYOUT.BLOCK_SIZE;
        const availableWidth = this.canvas.width * 0.8;
        const startX = (this.canvas.width - availableWidth) / 2;
        return startX + (availableWidth - gridWidth) / 2;
    }
    
    /**
     * 根据关卡初始化萌宠类型
     */
    initAnimalTypes() {
        const allAnimals = ['cat', 'dog', 'elephant', 'fox', 'frog', 'monkey', 'panda', 'rabbit', 'tiger'];
        let animalCount;
        switch (this.level) {
            case 1: animalCount = 5; break;
            case 2: animalCount = 7; break;
            default: animalCount = 9; break;
        }
        this.animalTypes = allAnimals.slice(0, animalCount);
    }
    
    /**
     * 初始化游戏
     */
    init() {
        console.log('初始化游戏页面');
        
        // 初始化网格
        this.initGrid();
        
        // 生成初始方块
        this.generateInitialBlocks();
        
        // 移除初始匹配
        this.removeInitialMatches();
        
        // 绑定事件
        this.bindEvents();
        
        console.log('游戏页面初始化完成');
    }
    
    /**
     * 初始化网格
     */
    initGrid() {
        this.grid = [];
        for (let row = 0; row < this.gridSizeY; row++) {
            this.grid[row] = [];
            for (let col = 0; col < this.gridSizeX; col++) {
                this.grid[row][col] = null;
            }
        }
    }
    
    /**
     * 生成初始方块
     */
    generateInitialBlocks() {
        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX; col++) {
                const block = this.createRandomBlock(row, col);
                this.grid[row][col] = block;
            }
        }
    }
    
    /**
     * 创建随机方块
     */
    createRandomBlock(row, col) {
        const animalType = this.animalTypes[Math.floor(Math.random() * this.animalTypes.length)];
        return {
            row: row,
            col: col,
            x: this.gridStartX + col * this.blockSize,
            y: this.gridStartY + row * this.blockSize,
            type: animalType,
            animalType: animalType,
            color: this.animalColors[animalType],
            blockType: 'normal',
            isSelected: false,
            isMatched: false,
            isRemoved: false,
            alpha: 1.0,
            scale: 1.0,
            rotation: 0
        };
    }
    
    /**
     * 创建特殊方块
     */
    createSpecialBlock(row, col, specialType) {
        return {
            row: row,
            col: col,
            x: this.gridStartX + col * this.blockSize,
            y: this.gridStartY + row * this.blockSize,
            type: specialType,
            animalType: specialType,
            color: this.specialColors[specialType],
            blockType: 'special',
            specialType: specialType,
            special: specialType,
            isSelected: false,
            isMatched: false,
            isRemoved: false,
            alpha: 1.0,
            scale: 1.0,
            rotation: 0
        };
    }
    
    /**
     * 移除初始匹配
     */
    removeInitialMatches() {
        let hasMatches = true;
        let attempts = 0;
        const maxAttempts = 100;
        
        while (hasMatches && attempts < maxAttempts) {
            hasMatches = false;
            attempts++;
            
            // 检查水平匹配
            for (let row = 0; row < this.gridSizeY; row++) {
                for (let col = 0; col < this.gridSizeX - 2; col++) {
                    const block1 = this.grid[row][col];
                    const block2 = this.grid[row][col + 1];
                    const block3 = this.grid[row][col + 2];
                    
                    if (block1 && block2 && block3 && 
                        block1.type === block2.type && 
                        block2.type === block3.type) {
                        this.grid[row][col + 2] = this.createRandomBlock(row, col + 2);
                        hasMatches = true;
                    }
                }
            }
            
            // 检查垂直匹配
            for (let col = 0; col < this.gridSizeX; col++) {
                for (let row = 0; row < this.gridSizeY - 2; row++) {
                    const block1 = this.grid[row][col];
                    const block2 = this.grid[row + 1][col];
                    const block3 = this.grid[row + 2][col];
                    
                    if (block1 && block2 && block3 && 
                        block1.type === block2.type && 
                        block2.type === block3.type) {
                        this.grid[row + 2][col] = this.createRandomBlock(row + 2, col);
                        hasMatches = true;
                    }
                }
            }
        }
        
        if (attempts >= maxAttempts) {
            console.warn('移除初始匹配达到最大尝试次数');
        }
    }
    
    /**
     * 更新分数并同步全局变量
     */
    updateScore(points) {
        this.score += points;
        window.gameScore = this.score;
        window.gameProgress = Math.min(this.score / this.targetScore, 1.0);
        console.log(`分数更新: +${points}, 总分: ${this.score}, 进度: ${(window.gameProgress * 100).toFixed(1)}%`);
    }
    
    /**
     * 重置分数并同步全局变量
     */
    resetScore() {
        this.score = 0;
        window.gameScore = this.score;
        window.gameProgress = 0;
        console.log('分数已重置');
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GamePage;
} else if (typeof window !== 'undefined') {
    window.GamePage = GamePage;
}
