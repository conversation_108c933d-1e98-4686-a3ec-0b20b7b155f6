/**
 * 排行榜页面模块 - 美化版
 * 负责显示全部、地区和好友排行功能
 */

// 导入背景工具类
let BackgroundUtils, BackgroundRenderer;
try {
    if (typeof require !== 'undefined') {
        const bgUtils = require('../utils/BackgroundUtils.js');
        BackgroundUtils = bgUtils.BackgroundUtils;
        BackgroundRenderer = bgUtils.BackgroundRenderer;
    } else {
        BackgroundUtils = window.BackgroundUtils;
        BackgroundRenderer = window.BackgroundRenderer;
    }
} catch (error) {
    console.warn('BackgroundUtils加载失败，使用简化版本');
}

class RankPage {
    constructor(gameManager) {
        this.gameManager = gameManager;
        this.canvas = gameManager.canvas;
        this.ctx = gameManager.ctx;
        this.buttons = [];
        this.animationTime = 0;
        this.currentTab = 'global';
        this.backgroundStars = null;
        
        // 排行榜数据 - 添加地区分类
        this.rankData = {
            global: [
                { name: '消除大师', score: 2500, avatar: '🏆', region: '北京' },
                { name: '萌宠达人', score: 2200, avatar: '🥈', region: '上海' },
                { name: '游戏高手', score: 2000, avatar: '🥉', region: '广州' },
                { name: '休闲玩家', score: 1800, avatar: '🎮', region: '深圳' },
                { name: '新手玩家', score: 1600, avatar: '🌟', region: '杭州' },
                { name: '积极玩家', score: 1400, avatar: '⭐', region: '成都' },
                { name: '努力玩家', score: 1200, avatar: '💪', region: '武汉' },
                { name: '坚持玩家', score: 1000, avatar: '🔥', region: '西安' },
                { name: '勤奋玩家', score: 800, avatar: '📈', region: '南京' },
                { name: '学习玩家', score: 600, avatar: '📚', region: '重庆' }
            ],
            region: [
                { name: '本地高手', score: 1950, avatar: '🏅', region: '本地' },
                { name: '同城达人', score: 1750, avatar: '🎯', region: '本地' },
                { name: '邻居玩家', score: 1550, avatar: '🎪', region: '本地' },
                { name: '附近高手', score: 1350, avatar: '🎨', region: '本地' },
                { name: '区域冠军', score: 1150, avatar: '🎭', region: '本地' },
                { name: '地区精英', score: 950, avatar: '🎪', region: '本地' },
                { name: '本地新星', score: 750, avatar: '⭐', region: '本地' },
                { name: '同城好手', score: 550, avatar: '🌟', region: '本地' },
                { name: '邻里高手', score: 350, avatar: '💫', region: '本地' },
                { name: '附近玩家', score: 150, avatar: '✨', region: '本地' }
            ],
            friend: [
                { name: '小明', score: 1900, avatar: '👨', relation: '好友' },
                { name: '小红', score: 1700, avatar: '👩', relation: '好友' },
                { name: '小李', score: 1500, avatar: '👦', relation: '好友' },
                { name: '小王', score: 1300, avatar: '👧', relation: '好友' },
                { name: '小张', score: 1100, avatar: '👨‍💼', relation: '好友' },
                { name: '小刘', score: 900, avatar: '👩‍💼', relation: '好友' },
                { name: '小陈', score: 700, avatar: '👨‍🎓', relation: '好友' },
                { name: '小林', score: 500, avatar: '👩‍🎓', relation: '好友' },
                { name: '小周', score: 300, avatar: '👨‍🏫', relation: '好友' },
                { name: '小吴', score: 100, avatar: '👩‍🏫', relation: '好友' }
            ]
        };
        
        this.init();
    }

    init() {
        console.log('初始化排行榜页面');
        this.initBackground();
        this.initButtons();
        this.bindEvents();
    }

    initBackground() {
        if (BackgroundUtils) {
            this.backgroundStars = BackgroundUtils.createStars(15, this.canvas.width, this.canvas.height);
        }
    }

    initButtons() {
        const centerX = this.canvas.width / 2;
        const tabWidth = 90;
        const tabSpacing = 10;
        const totalWidth = tabWidth * 3 + tabSpacing * 2;
        const startX = centerX - totalWidth / 2;
        
        this.buttons = [
            {
                id: 'globalRank',
                text: '全部',
                x: startX,
                y: 160,
                width: tabWidth,
                height: 40,
                color: this.currentTab === 'global' ? '#FF9800' : '#CCCCCC',
                action: () => this.switchTab('global')
            },
            {
                id: 'regionRank',
                text: '地区',
                x: startX + tabWidth + tabSpacing,
                y: 160,
                width: tabWidth,
                height: 40,
                color: this.currentTab === 'region' ? '#FF9800' : '#CCCCCC',
                action: () => this.switchTab('region')
            },
            {
                id: 'friendRank',
                text: '好友',
                x: startX + (tabWidth + tabSpacing) * 2,
                y: 160,
                width: tabWidth,
                height: 40,
                color: this.currentTab === 'friend' ? '#FF9800' : '#CCCCCC',
                action: () => this.switchTab('friend')
            },
            {
                id: 'back',
                text: '返回',
                x: 20,
                y: 70,
                width: 100,
                height: 32,
                action: () => this.goBack()
            }
        ];
    }

    switchTab(tab) {
        const tabNames = {
            'global': '全部',
            'region': '地区', 
            'friend': '好友'
        };
        console.log(`切换到${tabNames[tab]}排行榜`);
        this.currentTab = tab;
        this.initButtons(); // 重新初始化按钮以更新颜色状态
    }

    goBack() {
        console.log('返回主页面');
        this.gameManager.switchToPage('main');
    }

    bindEvents() {
        this.touchHandler = (res) => {
            if (res.touches && res.touches.length > 0) {
                const touch = res.touches[0];
                this.handleTouch(touch.clientX, touch.clientY);
            }
        };
        tt.onTouchStart(this.touchHandler);
    }

    handleTouch(x, y) {
        for (let button of this.buttons) {
            if (this.isPointInButton(x, y, button)) {
                console.log(`点击了按钮: ${button.text}`);
                button.action();
                break;
            }
        }
    }

    isPointInButton(x, y, button) {
        return x >= button.x && x <= button.x + button.width && y >= button.y && y <= button.y + button.height;
    }

    getCurrentRankData() {
        return this.rankData[this.currentTab] || [];
    }

    getPlayerRanking() {
        const currentData = this.getCurrentRankData();
        const playerScore = this.gameManager.gameData.playerData.bestScore;
        
        // 找到玩家在当前排行榜中的位置
        let ranking = currentData.findIndex(player => player.score <= playerScore);
        if (ranking === -1) {
            ranking = currentData.length; // 如果分数比所有人都低，排在最后
        }
        
        return ranking + 1; // 排名从1开始
    }

    update() {
        this.animationTime = (this.animationTime || 0) + 0.02;
        
        // 更新背景星星
        if (BackgroundUtils && this.backgroundStars) {
            BackgroundUtils.updateStars(this.backgroundStars);
        }
    }

    render() {
        // 使用背景工具类绘制统一背景
        if (BackgroundUtils && this.backgroundStars) {
            BackgroundUtils.updateStars(this.backgroundStars);
            BackgroundUtils.drawCompleteBackground(
                this.ctx,
                this.canvas.width,
                this.canvas.height,
                this.backgroundStars,
                this.animationTime || 0
            );
        } else {
            // 简化背景
            const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
            gradient.addColorStop(0, '#E1BEE7');
            gradient.addColorStop(1, '#F8BBD9');
            this.ctx.fillStyle = gradient;
            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        }
        
        const centerX = this.canvas.width / 2;
        
        // 绘制标题
        this.drawTitle(centerX);
        
        // 绘制玩家成绩卡片
        this.drawPlayerCard();
        
        // 绘制标签按钮
        this.drawTabButtons();
        
        // 绘制排行榜列表
        this.drawRankList();
        
        // 绘制返回按钮
        const backButton = this.buttons.find(btn => btn.id === 'back');
        if (backButton) {
            this.renderBackButton(backButton.x, backButton.y, backButton.width, backButton.height, backButton.text);
        }
    }

    drawTitle(centerX) {
        // 添加标题阴影效果
        this.ctx.save();
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        this.ctx.shadowBlur = 8;
        this.ctx.shadowOffsetX = 2;
        this.ctx.shadowOffsetY = 2;
        
        // 使用渐变色标题
        const gradient = this.ctx.createLinearGradient(centerX - 80, 70, centerX + 80, 100);
        gradient.addColorStop(0, '#FF9800');  // 橙色
        gradient.addColorStop(1, '#E65100');  // 深橙色
        
        this.ctx.fillStyle = gradient;
        this.ctx.font = 'bold 36px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('排行榜', centerX, 100);
        
        // 添加装饰性下划线
        this.ctx.beginPath();
        this.ctx.moveTo(centerX - 80, 110);
        this.ctx.lineTo(centerX + 80, 110);
        this.ctx.lineWidth = 3;
        this.ctx.strokeStyle = '#FF9800';
        this.ctx.stroke();
        
        this.ctx.restore();
    }

    drawPlayerCard() {
        const cardY = 220;
        const cardHeight = 90;
        
        // 添加卡片阴影
        this.ctx.save();
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        this.ctx.shadowBlur = 10;
        this.ctx.shadowOffsetX = 3;
        this.ctx.shadowOffsetY = 3;
        
        // 使用渐变背景
        const gradient = this.ctx.createLinearGradient(0, cardY, 0, cardY + cardHeight);
        gradient.addColorStop(0, 'rgba(33, 33, 33, 0.8)');
        gradient.addColorStop(1, 'rgba(66, 66, 66, 0.8)');
        
        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.ctx.roundRect(50, cardY, this.canvas.width - 100, cardHeight, 15);
        this.ctx.fill();
        
        // 添加卡片边框
        this.ctx.strokeStyle = 'rgba(255, 152, 0, 0.5)';
        this.ctx.lineWidth = 2;
        this.ctx.stroke();
        this.ctx.restore();
        
        // 添加玩家头像/图标
        this.ctx.fillStyle = '#FF9800';
        this.ctx.beginPath();
        this.ctx.arc(90, cardY + cardHeight/2, 25, 0, Math.PI * 2);
        this.ctx.fill();
        
        // 在头像中添加玩家初始字母或图标
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = 'bold 24px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText('P', 90, cardY + cardHeight/2);
        this.ctx.textBaseline = 'alphabetic';
        
        // 绘制玩家信息 - 使用更现代的布局
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = 'bold 18px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'left';
        this.ctx.fillText('我的最佳成绩', 130, cardY + 30);
        
        // 使用更醒目的分数显示
        this.ctx.fillStyle = '#FFD54F'; // 更亮的金色
        this.ctx.font = 'bold 28px Arial';
        this.ctx.fillText(`${this.gameManager.gameData.playerData.bestScore} 分`, 130, cardY + 65);
        
        // 绘制排名信息 - 添加排名图标
        const ranking = this.getPlayerRanking();
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = 'bold 16px Arial';
        this.ctx.textAlign = 'right';
        
        // 根据排名显示不同颜色
        let rankColor = '#FFFFFF';
        if (ranking <= 3) {
            rankColor = '#FFD700'; // 金色
        } else if (ranking <= 10) {
            rankColor = '#E0E0E0'; // 银色
        }
        
        this.ctx.fillStyle = '#BBDEFB';
        this.ctx.fillText('当前排名:', this.canvas.width - 120, cardY + 45);
        this.ctx.fillStyle = rankColor;
        this.ctx.font = 'bold 24px Arial';
        this.ctx.fillText(`#${ranking}`, this.canvas.width - 70, cardY + 45);
    }

    drawTabButtons() {
        const centerX = this.canvas.width / 2;
        const tabY = 160;
        const tabWidth = 280;
        const tabHeight = 40;
        
        // 绘制选项卡背景
        this.ctx.save();
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
        this.ctx.beginPath();
        this.ctx.roundRect(centerX - tabWidth/2, tabY, tabWidth, tabHeight, 20);
        this.ctx.fill();
        
        // 绘制选项卡按钮
        for (let button of this.buttons) {
            if (button.id === 'globalRank' || button.id === 'regionRank' || button.id === 'friendRank') {
                this.drawTabButton(button, tabY, tabHeight);
            }
        }
        this.ctx.restore();
    }

    drawTabButton(button, tabY, tabHeight) {
        // 判断按钮是否激活
        const isActive = (button.id === 'globalRank' && this.currentTab === 'global') || 
                         (button.id === 'regionRank' && this.currentTab === 'region') ||
                         (button.id === 'friendRank' && this.currentTab === 'friend');
        
        // 绘制按钮背景 - 只为激活的按钮绘制背景
        if (isActive) {
            // 创建渐变背景
            const gradient = this.ctx.createLinearGradient(
                button.x, button.y, 
                button.x, button.y + button.height
            );
            gradient.addColorStop(0, '#FF9800');
            gradient.addColorStop(1, '#F57C00');
            
            this.ctx.fillStyle = gradient;
            this.ctx.beginPath();
            this.ctx.roundRect(button.x, button.y, button.width, button.height, 20);
            this.ctx.fill();
            
            // 添加光泽效果
            const glossGradient = this.ctx.createLinearGradient(
                button.x, button.y, 
                button.x, button.y + button.height / 2
            );
            glossGradient.addColorStop(0, 'rgba(255, 255, 255, 0.3)');
            glossGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
            this.ctx.fillStyle = glossGradient;
            this.ctx.beginPath();
            this.ctx.roundRect(button.x, button.y, button.width, button.height / 2, 20);
            this.ctx.fill();
        }
        
        // 绘制按钮文字
        this.ctx.fillStyle = isActive ? '#FFFFFF' : '#CCCCCC';
        this.ctx.font = isActive ? 'bold 18px Arial, "Microsoft YaHei"' : '16px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        
        // 添加文字阴影效果（仅激活按钮）
        if (isActive) {
            this.ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
            this.ctx.shadowBlur = 3;
            this.ctx.shadowOffsetX = 1;
            this.ctx.shadowOffsetY = 1;
        }
        
        this.ctx.fillText(button.text, button.x + button.width / 2, button.y + button.height / 2);
        this.ctx.shadowColor = 'transparent';
        this.ctx.textBaseline = 'alphabetic';
        
        // 为激活按钮添加底部指示条
        if (isActive) {
            this.ctx.fillStyle = '#FFFFFF';
            this.ctx.fillRect(button.x + 20, button.y + button.height - 3, button.width - 40, 3);
        }
    }

    drawRankList() {
        const rankData = this.getCurrentRankData();
        const startY = 360;
        const itemHeight = 50;
        
        // 添加列表阴影
        this.ctx.save();
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        this.ctx.shadowBlur = 10;
        this.ctx.shadowOffsetX = 3;
        this.ctx.shadowOffsetY = 3;
        
        // 绘制列表背景框 - 使用渐变背景
        const listHeight = Math.min(rankData.length, 10) * itemHeight + 80;
        const gradient = this.ctx.createLinearGradient(0, startY - 30, 0, startY - 30 + listHeight);
        gradient.addColorStop(0, 'rgba(33, 33, 33, 0.8)');
        gradient.addColorStop(1, 'rgba(66, 66, 66, 0.8)');
        
        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.ctx.roundRect(40, startY - 30, this.canvas.width - 80, listHeight, 15);
        this.ctx.fill();
        
        // 添加列表边框
        this.ctx.strokeStyle = 'rgba(255, 152, 0, 0.3)';
        this.ctx.lineWidth = 2;
        this.ctx.stroke();
        this.ctx.restore();
        
        // 绘制表头
        this.drawListHeader(startY);
        
        // 显示前10名玩家
        const displayData = rankData.slice(0, 10);
        displayData.forEach((player, index) => {
            this.drawRankItem(player, index, startY + 30 + index * itemHeight);
        });
    }

    drawListHeader(startY) {
        // 使用更现代的表头样式
        this.ctx.fillStyle = '#FFB74D'; // 浅橙色
        this.ctx.font = 'bold 16px Arial, "Microsoft YaHei"';
        
        // 排名列
        this.ctx.textAlign = 'center';
        this.ctx.fillText('排名', 70, startY);
        
        // 玩家列
        this.ctx.textAlign = 'left';
        this.ctx.fillText('玩家', 150, startY);
        
        // 分数列
        this.ctx.textAlign = 'right';
        this.ctx.fillText('分数', this.canvas.width - 70, startY);
        
        // 绘制表头分割线 - 使用渐变线
        const gradient = this.ctx.createLinearGradient(50, startY + 10, this.canvas.width - 50, startY + 10);
        gradient.addColorStop(0, 'rgba(255, 152, 0, 0.1)');
        gradient.addColorStop(0.5, 'rgba(255, 152, 0, 0.5)');
        gradient.addColorStop(1, 'rgba(255, 152, 0, 0.1)');
        
        this.ctx.strokeStyle = gradient;
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.moveTo(50, startY + 10);
        this.ctx.lineTo(this.canvas.width - 50, startY + 10);
        this.ctx.stroke();
    }

    drawRankItem(player, index, y) {
        const isTopThree = index < 3;
        const isEvenRow = index % 2 === 0;
        
        // 为偶数行添加轻微背景色
        if (isEvenRow) {
            this.ctx.fillStyle = 'rgba(255, 255, 255, 0.05)';
            this.ctx.fillRect(50, y - 15, this.canvas.width - 100, 40);
        }
        
        // 绘制排名 - 使用特殊样式
        if (isTopThree) {
            // 为前三名绘制奖牌背景
            const medalColors = ['#FFD700', '#C0C0C0', '#CD7F32'];
            this.ctx.fillStyle = medalColors[index];
            this.ctx.beginPath();
            this.ctx.arc(70, y - 5, 15, 0, Math.PI * 2);
            this.ctx.fill();
            
            // 绘制排名数字
            this.ctx.fillStyle = '#333333';
            this.ctx.font = 'bold 16px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';
            this.ctx.fillText(`${index + 1}`, 70, y - 5);
            this.ctx.textBaseline = 'alphabetic';
        } else {
            // 其他排名使用普通样式
            this.ctx.fillStyle = '#BBDEFB'; // 浅蓝色
            this.ctx.font = '14px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillText(`${index + 1}`, 70, y);
        }
        
        // 绘制头像背景
        this.ctx.fillStyle = isTopThree ? '#FFB74D' : '#78909C';
        this.ctx.beginPath();
        this.ctx.arc(110, y - 5, 15, 0, Math.PI * 2);
        this.ctx.fill();
        
        // 绘制头像内容
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = '16px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText(player.avatar, 110, y - 5);
        this.ctx.textBaseline = 'alphabetic';
        
        // 绘制玩家名称
        this.ctx.fillStyle = isTopThree ? '#FFD54F' : '#FFFFFF';
        this.ctx.font = isTopThree ? 'bold 16px Arial, "Microsoft YaHei"' : '14px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'left';
        this.ctx.fillText(player.name, 140, y);
        
        // 绘制分数
        const scoreColor = isTopThree ? 
            ['#FFD700', '#E0E0E0', '#CD7F32'][index] : // 金、银、铜
            '#FFFFFF';
            
        this.ctx.fillStyle = scoreColor;
        this.ctx.font = isTopThree ? 'bold 18px Arial' : '16px Arial';
        this.ctx.textAlign = 'right';
        this.ctx.fillText(`${player.score}分`, this.canvas.width - 70, y);
        
        // 为前三名添加特殊标记
        if (isTopThree) {
            const icons = ['👑', '🥈', '🥉'];
            this.ctx.font = '16px Arial';
            this.ctx.textAlign = 'left';
            this.ctx.fillText(icons[index], this.canvas.width - 60, y);
        }
    }

    // 渲染返回按钮（半透明白色背景，黑色字体）
    renderBackButton(x, y, width, height, text) {
        this.ctx.save();
        
        // 按钮阴影
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
        this.ctx.shadowBlur = 6;
        this.ctx.shadowOffsetX = 2;
        this.ctx.shadowOffsetY = 2;
        
        // 半透明白色背景（透明度0.6）
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
        this.ctx.beginPath();
        this.ctx.roundRect(x, y, width, height, 15);
        this.ctx.fill();
        
        // 按钮边框
        this.ctx.shadowColor = 'transparent';
        this.ctx.strokeStyle = 'rgba(0, 0, 0, 0.3)';
        this.ctx.lineWidth = 1;
        this.ctx.stroke();
        
        // 黑色文字（确保完全居中）
        this.ctx.fillStyle = '#333333';
        this.ctx.font = 'bold 16px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        
        // 精确计算文本位置以确保完全居中
        const textX = Math.round(x + width / 2);
        const textY = Math.round(y + height / 2);
        this.ctx.fillText(text, textX, textY);
        
        this.ctx.restore();
    }

    destroy() {
        console.log('销毁排行榜页面');
        if (this.touchHandler) {
            tt.offTouchStart(this.touchHandler);
        }
        this.buttons = [];
        this.backgroundStars = null;
    }
}

// 导出模块 - 抖音小游戏环境兼容
if (typeof module !== 'undefined' && module.exports) {
    module.exports = RankPage;
} else {
    // 全局注册
    window.RankPage = RankPage;
}