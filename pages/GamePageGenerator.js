/**
 * 游戏页面生成器 - 处理新方块的生成和管理
 */
class GamePageGenerator {
    constructor(core) {
        this.core = core;
        this.canvas = core.canvas;
        this.ctx = core.ctx;
        
        // 生成配置
        this.generationDelay = 100; // 生成间隔（毫秒）
        this.maxQueueSize = 20; // 最大队列大小
        
        // 生成队列
        this.generationQueue = [];
        this.isGenerating = false;
        
        console.log('GamePageGenerator初始化完成');
    }
    
    // 请求生成新方块
    requestGeneration(col, count) {
        console.log(`请求为列${col}生成${count}个方块`);
        
        for (let i = 0; i < count; i++) {
            this.generationQueue.push({
                col: col,
                delay: i * this.generationDelay,
                timestamp: Date.now()
            });
        }
        
        // 开始生成过程
        if (!this.isGenerating) {
            this.startGeneration();
        }
    }
    
    // 开始生成过程
    startGeneration() {
        this.isGenerating = true;
        this.processGenerationQueue();
    }
    
    // 处理生成队列
    processGenerationQueue() {
        if (this.generationQueue.length === 0) {
            this.isGenerating = false;
            return;
        }
        
        const currentTime = Date.now();
        const readyItems = [];
        
        // 找到准备生成的项目
        this.generationQueue.forEach((item, index) => {
            if (currentTime >= item.timestamp + item.delay) {
                readyItems.push({ item, index });
            }
        });
        
        // 生成准备好的方块
        readyItems.reverse().forEach(({ item, index }) => {
            this.generateBlock(item.col);
            this.generationQueue.splice(index, 1);
        });
        
        // 继续处理队列
        if (this.generationQueue.length > 0) {
            setTimeout(() => this.processGenerationQueue(), 50);
        } else {
            this.isGenerating = false;
        }
    }
    
    // 生成单个方块
    generateBlock(col) {
        // 找到该列最顶部的空位
        const targetRow = this.findTopEmptyRow(col);
        
        if (targetRow < 0) {
            console.warn(`列${col}已满，无法生成新方块`);
            return null;
        }
        
        // 创建新方块
        const newBlock = this.core.createRandomBlock(
            targetRow,
            col,
            this.core.gridStartX,
            this.core.gridStartY
        );
        
        if (newBlock) {
            // 设置初始状态
            newBlock.isNewGenerated = true;
            newBlock.generationTime = Date.now();
            newBlock.scale = 0.3; // 从小开始
            newBlock.alpha = 0.7;
            
            // 添加生成动画
            this.addGenerationAnimation(newBlock);
            
            console.log(`生成新方块: (${targetRow}, ${col}), 类型: ${newBlock.type}`);
            return newBlock;
        }
        
        return null;
    }
    
    // 找到列中最顶部的空位
    findTopEmptyRow(col) {
        for (let row = 0; row < this.core.gridSizeY; row++) {
            if (!this.core.grid[row] || !this.core.grid[row][col] || this.core.grid[row][col].isRemoved) {
                return row;
            }
        }
        return -1; // 列已满
    }
    
    // 添加生成动画
    addGenerationAnimation(block) {
        const startScale = 0.3;
        const targetScale = 1.0;
        const startAlpha = 0.7;
        const targetAlpha = 1.0;
        const duration = 300; // 动画持续时间
        
        const startTime = Date.now();
        
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // 使用缓动函数
            const easeProgress = this.easeOutBounce(progress);
            
            // 更新缩放和透明度
            block.scale = startScale + (targetScale - startScale) * easeProgress;
            block.alpha = startAlpha + (targetAlpha - startAlpha) * progress;
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                // 动画完成
                block.scale = targetScale;
                block.alpha = targetAlpha;
                block.isNewGenerated = false;
            }
        };
        
        animate();
    }
    
    // 缓动函数 - 弹跳效果
    easeOutBounce(t) {
        if (t < 1 / 2.75) {
            return 7.5625 * t * t;
        } else if (t < 2 / 2.75) {
            return 7.5625 * (t -= 1.5 / 2.75) * t + 0.75;
        } else if (t < 2.5 / 2.75) {
            return 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375;
        } else {
            return 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375;
        }
    }
    
    // 批量生成方块填充空位
    fillEmptySpaces() {
        console.log('开始填充空位');
        
        const emptySpaces = this.findAllEmptySpaces();
        
        // 按列分组
        const columnGroups = {};
        emptySpaces.forEach(space => {
            if (!columnGroups[space.col]) {
                columnGroups[space.col] = [];
            }
            columnGroups[space.col].push(space);
        });
        
        // 为每列请求生成
        Object.keys(columnGroups).forEach(col => {
            const count = columnGroups[col].length;
            this.requestGeneration(parseInt(col), count);
        });
    }
    
    // 找到所有空位
    findAllEmptySpaces() {
        const emptySpaces = [];
        
        for (let row = 0; row < this.core.gridSizeY; row++) {
            for (let col = 0; col < this.core.gridSizeX; col++) {
                if (!this.core.grid[row] || !this.core.grid[row][col] || this.core.grid[row][col].isRemoved) {
                    emptySpaces.push({ row, col });
                }
            }
        }
        
        return emptySpaces;
    }
    
    // 预生成方块（用于优化性能）
    preGenerateBlocks(count = 10) {
        const preGeneratedBlocks = [];
        
        for (let i = 0; i < count; i++) {
            const block = this.core.createRandomBlock(0, 0, 0, 0);
            if (block) {
                preGeneratedBlocks.push(block);
            }
        }
        
        return preGeneratedBlocks;
    }
    
    // 检查是否正在生成
    isGeneratingBlocks() {
        return this.isGenerating || this.generationQueue.length > 0;
    }
    
    // 获取生成统计信息
    getGenerationStats() {
        return {
            queueSize: this.generationQueue.length,
            isGenerating: this.isGenerating,
            maxQueueSize: this.maxQueueSize
        };
    }
    
    // 清理生成器
    clear() {
        this.generationQueue = [];
        this.isGenerating = false;
    }
    
    // 暂停生成
    pause() {
        this.isGenerating = false;
    }
    
    // 恢复生成
    resume() {
        if (this.generationQueue.length > 0 && !this.isGenerating) {
            this.startGeneration();
        }
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GamePageGenerator;
} else {
    window.GamePageGenerator = GamePageGenerator;
}