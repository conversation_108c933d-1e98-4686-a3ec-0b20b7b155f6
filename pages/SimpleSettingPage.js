/**
 * 简化的设置页面类
 */
class SimpleSettingPage {
    constructor(gameManager) {
        this.gameManager = gameManager;
        this.canvas = gameManager.canvas;
        this.ctx = gameManager.ctx;

        // 从缓存加载设置或使用默认值
        this.loadSettings();

        // 返回按钮
        this.backButton = {
            x: 50,
            y: 50,
            width: 100,
            height: 40,
            text: '返回',
            color: '#FF6B6B'
        };

        // 拖拽状态
        this.isDragging = false;
        this.dragSliderIndex = -1;

        // UI配置
        this.uiConfig = null;
        this.initUIConfig();

        console.log('SimpleSettingPage初始化完成');
    }

    // 初始化UI配置
    initUIConfig() {
        try {
            if (typeof GameUIConfig !== 'undefined') {
                this.uiConfig = new GameUIConfig();
                console.log('设置页面UI配置初始化成功');
            } else {
                console.warn('GameUIConfig未加载，使用默认配置');
                this.uiConfig = null;
            }
        } catch (error) {
            console.error('初始化UI配置时出错:', error);
            this.uiConfig = null;
        }
    }

    // 加载设置
    loadSettings() {
        try {
            const savedSettings = tt.getStorageSync('gameSettings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);
                this.settings = [
                    { name: '背景音乐音量', value: Math.round(settings.bgmVolume * 100), type: 'slider', key: 'bgmVolume' },
                    { name: '音效音量', value: Math.round(settings.effectVolume * 100), type: 'slider', key: 'effectVolume' },
                    { name: '静音模式', value: settings.mute, type: 'toggle', key: 'mute' },
                    { name: '显示提示', value: settings.showTips, type: 'toggle', key: 'showTips' }
                ];
                console.log('从缓存加载设置成功');
            } else {
                // 使用默认设置并保存到缓存
                this.settings = [
                    { name: '背景音乐音量', value: 50, type: 'slider', key: 'bgmVolume' },
                    { name: '音效音量', value: 50, type: 'slider', key: 'effectVolume' },
                    { name: '静音模式', value: false, type: 'toggle', key: 'mute' },
                    { name: '显示提示', value: true, type: 'toggle', key: 'showTips' }
                ];
                this.saveSettings();
                console.log('使用默认设置并保存到缓存');
            }
        } catch (error) {
            console.error('加载设置失败:', error);
            // 使用默认设置
            this.settings = [
                { name: '背景音乐音量', value: 50, type: 'slider', key: 'bgmVolume' },
                { name: '音效音量', value: 50, type: 'slider', key: 'effectVolume' },
                { name: '静音模式', value: false, type: 'toggle', key: 'mute' },
                { name: '显示提示', value: true, type: 'toggle', key: 'showTips' }
            ];
        }
    }

    // 保存设置到缓存
    saveSettings() {
        try {
            const settingsData = {
                bgmVolume: this.getSetting('bgmVolume') / 100,
                effectVolume: this.getSetting('effectVolume') / 100,
                mute: this.getSetting('mute'),
                muteMode: this.getSetting('mute'),
                showTips: this.getSetting('showTips'),
                autoSave: true
            };

            tt.setStorageSync('gameSettings', JSON.stringify(settingsData));

            // 同时更新游戏管理器中的设置
            if (this.gameManager.gameData && this.gameManager.gameData.settings) {
                Object.assign(this.gameManager.gameData.settings, settingsData);
            }

            console.log('设置已保存到缓存');
        } catch (error) {
            console.error('保存设置失败:', error);
        }
    }

    // 获取设置值
    getSetting(key) {
        const setting = this.settings.find(s => s.key === key);
        return setting ? setting.value : null;
    }
    
    init() {
        console.log('初始化设置页面');
        this.setupTouchEvents();
        console.log('设置页面初始化完成');
    }
    
    handleTouchStart(x, y) {
        // 检查返回按钮点击
        if (this.isPointInButton(x, y, this.backButton)) {
            console.log('点击返回按钮');
            this.gameManager.switchToPage('main');
            return;
        }

        // 检查设置项点击
        this.settings.forEach((setting, index) => {
            const settingY = 200 + index * 100;

            if (setting.type === 'toggle') {
                const toggleX = this.canvas.width - 150;
                const toggleY = settingY - 20;
                const toggleWidth = 60;
                const toggleHeight = 40;

                if (x >= toggleX && x <= toggleX + toggleWidth &&
                    y >= toggleY && y <= toggleY + toggleHeight) {
                    setting.value = !setting.value;
                    console.log(`切换设置: ${setting.name} = ${setting.value}`);
                    this.saveSettings(); // 实时保存
                }
            } else if (setting.type === 'slider') {
                // 检查滑块区域点击
                const sliderX = this.canvas.width - 180; // 调整滑块位置，避免数值被挤出
                const sliderWidth = 100; // 缩短滑块长度
                const sliderHeight = 20;

                if (x >= sliderX && x <= sliderX + sliderWidth &&
                    y >= settingY - sliderHeight/2 && y <= settingY + sliderHeight/2) {
                    // 开始拖拽
                    this.isDragging = true;
                    this.dragSliderIndex = index;
                    this.updateSliderValue(x, index);
                }
            }
        });
    }

    // 处理触摸移动
    handleTouchMove(x, y) {
        if (this.isDragging && this.dragSliderIndex >= 0) {
            this.updateSliderValue(x, this.dragSliderIndex);
        }
    }

    // 处理触摸结束
    handleTouchEnd(x, y) {
        if (this.isDragging) {
            this.isDragging = false;
            this.dragSliderIndex = -1;
            this.saveSettings(); // 拖拽结束时保存设置
        }
    }

    // 更新滑块值
    updateSliderValue(x, index) {
        const setting = this.settings[index];
        if (setting.type !== 'slider') return;

        // 使用动态计算的布局
        const containerWidth = this.canvas.width - 100; // 减去左右边距
        const layout = this.uiConfig ?
            this.uiConfig.calculateSliderLayout(containerWidth, 80) :
            { sliderWidth: 100, sliderX: this.canvas.width - 180 };

        const sliderX = 50 + layout.sliderX; // 加上左边距
        const sliderWidth = layout.sliderWidth;

        // 计算新值
        const progress = Math.max(0, Math.min(1, (x - sliderX) / sliderWidth));
        const newValue = Math.round(progress * 100);

        if (setting.value !== newValue) {
            setting.value = newValue;
            console.log(`调整设置: ${setting.name} = ${setting.value}%`);

            // 实时应用音量设置
            if (this.gameManager.audioManager) {
                if (setting.key === 'bgmVolume') {
                    this.gameManager.audioManager.setMusicVolume(newValue / 100);
                } else if (setting.key === 'effectVolume') {
                    this.gameManager.audioManager.setSoundVolume(newValue / 100);
                }
            }
        }
    }
    
    isPointInButton(x, y, button) {
        return x >= button.x && x <= button.x + button.width &&
               y >= button.y && y <= button.y + button.height;
    }
    
    update() {
        // 设置页面暂时不需要动画更新
    }
    
    render() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        this.renderBackground();
        // this.renderTitle();
        this.renderSettings();
        this.renderBackButton();
    }
    
    renderBackground() {
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        gradient.addColorStop(0, '#f093fb');
        gradient.addColorStop(1, '#f5576c');
        
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }
    
    renderTitle() {
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = 'bold 36px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText('游戏设置', this.canvas.width / 2, 120);
    }
    
    renderSettings() {
        this.settings.forEach((setting, index) => {
            const y = 200 + index * 100;
            
            // 绘制设置项背景
            this.ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
            this.ctx.fillRect(50, y - 40, this.canvas.width - 100, 80);
            
            // 绘制设置名称
            this.ctx.fillStyle = '#FFFFFF';
            this.ctx.font = '20px Arial, "Microsoft YaHei"';
            this.ctx.textAlign = 'left';
            this.ctx.textBaseline = 'middle';
            this.ctx.fillText(setting.name, 80, y);
            
            // 绘制设置值
            if (setting.type === 'slider') {
                // 使用动态计算的布局
                const containerWidth = this.canvas.width - 100; // 减去左右边距
                const layout = this.uiConfig ?
                    this.uiConfig.calculateSliderLayout(containerWidth, 80) :
                    { sliderWidth: 100, sliderX: this.canvas.width - 180, valueX: this.canvas.width - 70, valueWidth: 50 };

                const sliderConfig = this.uiConfig ? this.uiConfig.getSettingPageConfig().slider : null;

                // 计算实际位置（加上左边距）
                const sliderX = 50 + layout.sliderX;
                const sliderWidth = layout.sliderWidth;
                const valueX = 50 + layout.valueX;
                const sliderHeight = sliderConfig ? sliderConfig.trackHeight : 8;

                // 滑块背景
                this.ctx.fillStyle = sliderConfig ? sliderConfig.trackColor : 'rgba(255, 255, 255, 0.3)';
                this.ctx.fillRect(sliderX, y - sliderHeight/2, sliderWidth, sliderHeight);

                // 滑块进度
                const progress = setting.value / 100;
                this.ctx.fillStyle = sliderConfig ? sliderConfig.fillColor : '#4CAF50';
                this.ctx.fillRect(sliderX, y - sliderHeight/2, sliderWidth * progress, sliderHeight);

                // 滑块圆点
                const dotX = sliderX + sliderWidth * progress;
                const thumbSize = sliderConfig ? sliderConfig.thumbSize : 12;

                this.ctx.beginPath();
                this.ctx.arc(dotX, y, thumbSize, 0, Math.PI * 2);
                this.ctx.fillStyle = sliderConfig ? sliderConfig.thumbColor : '#FFFFFF';
                this.ctx.fill();

                // 添加圆点边框
                if (sliderConfig && sliderConfig.thumbBorderWidth > 0) {
                    this.ctx.beginPath();
                    this.ctx.arc(dotX, y, thumbSize, 0, Math.PI * 2);
                    this.ctx.strokeStyle = sliderConfig.thumbBorderColor;
                    this.ctx.lineWidth = sliderConfig.thumbBorderWidth;
                    this.ctx.stroke();
                }

                // 显示数值 - 使用动态计算的位置
                this.ctx.fillStyle = sliderConfig ? sliderConfig.valueColor : '#FFFFFF';
                this.ctx.font = sliderConfig ? sliderConfig.valueFont : '16px Arial, "Microsoft YaHei"';
                this.ctx.textAlign = 'center';
                this.ctx.fillText(`${setting.value}%`, valueX + layout.valueWidth / 2, y);
                
            } else if (setting.type === 'toggle') {
                // 绘制开关
                const toggleX = this.canvas.width - 150;
                const toggleY = y - 20;
                const toggleWidth = 60;
                const toggleHeight = 40;
                
                // 开关背景
                this.ctx.fillStyle = setting.value ? '#4CAF50' : '#666666';
                this.ctx.fillRect(toggleX, toggleY, toggleWidth, toggleHeight);
                
                // 开关圆点
                const dotX = setting.value ? toggleX + toggleWidth - 20 : toggleX + 20;
                this.ctx.beginPath();
                this.ctx.arc(dotX, toggleY + toggleHeight/2, 15, 0, Math.PI * 2);
                this.ctx.fillStyle = '#FFFFFF';
                this.ctx.fill();
                
                // 显示状态文字
                this.ctx.fillStyle = '#FFFFFF';
                this.ctx.font = '16px Arial, "Microsoft YaHei"';
                this.ctx.textAlign = 'center';
                this.ctx.fillText(setting.value ? '开' : '关', toggleX + toggleWidth + 30, y);
            }
        });
    }
    
    renderBackButton() {
        // 绘制返回按钮
        this.ctx.fillStyle = this.backButton.color;
        this.ctx.fillRect(this.backButton.x, this.backButton.y, this.backButton.width, this.backButton.height);
        
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = 'bold 16px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText(
            this.backButton.text,
            this.backButton.x + this.backButton.width / 2,
            this.backButton.y + this.backButton.height / 2
        );
    }
    
    setupTouchEvents() {
        if (typeof tt !== 'undefined') {
            this.touchStartHandler = (e) => {
                if (e.touches && e.touches.length > 0) {
                    const touch = e.touches[0];
                    this.handleTouchStart(touch.clientX, touch.clientY);
                }
            };

            this.touchMoveHandler = (e) => {
                if (e.touches && e.touches.length > 0) {
                    const touch = e.touches[0];
                    this.handleTouchMove(touch.clientX, touch.clientY);
                }
            };

            this.touchEndHandler = (e) => {
                this.handleTouchEnd();
            };

            tt.onTouchStart(this.touchStartHandler);
            tt.onTouchMove(this.touchMoveHandler);
            tt.onTouchEnd(this.touchEndHandler);
        }
    }
    
    destroy() {
        console.log('清理SimpleSettingPage资源');
        if (typeof tt !== 'undefined') {
            if (this.touchStartHandler) {
                tt.offTouchStart(this.touchStartHandler);
            }
            if (this.touchMoveHandler) {
                tt.offTouchMove(this.touchMoveHandler);
            }
            if (this.touchEndHandler) {
                tt.offTouchEnd(this.touchEndHandler);
            }
        }
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SimpleSettingPage;
} else {
    window.SimpleSettingPage = SimpleSettingPage;
}