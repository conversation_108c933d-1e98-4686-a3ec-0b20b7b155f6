/**
 * 简化的游戏页面渲染器
 * 删除所有复杂的渲染逻辑，只保留最基本的功能
 */
class SimpleGamePageRenderer {
    constructor(canvas, ctx, core, uiConfig) {
        this.canvas = canvas;
        this.ctx = ctx;
        this.core = core;
        this.uiConfig = uiConfig;
    }
    
    // 主渲染方法
    render() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 渲染背景
        this.renderBackground();
        
        // 渲染统计栏
        this.renderStatsBar();
        
        // 渲染网格
        this.renderGrid();
        
        // 渲染方块
        this.renderBlocks();
        
        // 渲染道具栏
        this.renderPropBar();
    }
    
    // 渲染背景
    renderBackground() {
        const bgConfig = this.uiConfig.getMainPageBackgroundConfig();
        const gradient = this.ctx.createRadialGradient(
            this.canvas.width / 2, this.canvas.height / 2, 0,
            this.canvas.width / 2, this.canvas.height / 2, Math.max(this.canvas.width, this.canvas.height) / 2
        );
        
        bgConfig.gradient.colors.forEach((color, index) => {
            gradient.addColorStop(index / (bgConfig.gradient.colors.length - 1), color);
        });
        
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }
    
    // 渲染统计栏
    renderStatsBar() {
        const config = this.uiConfig.getStatsBarConfig();
        
        // 绘制背景
        this.ctx.fillStyle = config.backgroundColor;
        this.ctx.beginPath();
        this.ctx.roundRect(config.x, config.y, config.width, config.height, config.borderRadius);
        this.ctx.fill();
        
        // 绘制文字
        this.ctx.fillStyle = config.textColor;
        this.ctx.font = `${config.fontSize}px Arial`;
        this.ctx.textAlign = 'left';
        this.ctx.fillText(`分数: ${this.core.score}`, config.x + 20, config.y + 35);
        this.ctx.fillText(`等级: ${this.core.level}`, config.x + config.width - 120, config.y + 35);
    }
    
    // 渲染网格
    renderGrid() {
        const config = this.uiConfig.getGridConfig();
        
        // 绘制网格背景
        this.ctx.fillStyle = config.backgroundColor;
        this.ctx.beginPath();
        this.ctx.roundRect(config.startX, config.startY, config.width, config.height, config.borderRadius);
        this.ctx.fill();
        
        // 绘制格子背景
        const cellSize = (config.width - config.cellSpacing * (this.core.gridSizeX - 1)) / this.core.gridSizeX;
        
        for (let row = 0; row < this.core.gridSizeY; row++) {
            for (let col = 0; col < this.core.gridSizeX; col++) {
                const x = config.startX + col * (cellSize + config.cellSpacing) + config.cellInset;
                const y = config.startY + row * (cellSize + config.cellSpacing) + config.cellInset;
                const size = cellSize - config.cellInset * 2;
                
                this.ctx.fillStyle = config.cellBackgroundColor;
                this.ctx.beginPath();
                this.ctx.roundRect(x, y, size, size, 8);
                this.ctx.fill();
            }
        }
    }
    
    // 渲染方块
    renderBlocks() {
        const config = this.uiConfig.getGridConfig();
        const cellSize = (config.width - config.cellSpacing * (this.core.gridSizeX - 1)) / this.core.gridSizeX;
        
        this.core.grid.forEach((row, rowIndex) => {
            row.forEach((block, colIndex) => {
                if (block && block.type) {
                    const x = config.startX + colIndex * (cellSize + config.cellSpacing);
                    const y = config.startY + rowIndex * (cellSize + config.cellSpacing);
                    
                    this.renderBlock(block, x, y, cellSize);
                }
            });
        });
    }
    
    // 渲染单个方块
    renderBlock(block, x, y, size) {
        // 获取方块颜色
        const color = this.getBlockColor(block.type);
        
        // 绘制方块背景
        this.ctx.fillStyle = color;
        this.ctx.beginPath();
        this.ctx.roundRect(x + 5, y + 5, size - 10, size - 10, 8);
        this.ctx.fill();
        
        // 绘制方块图片或文字
        if (block.image && block.image.complete) {
            this.ctx.drawImage(block.image, x + 10, y + 10, size - 20, size - 20);
        } else {
            // 后备方案：显示文字
            this.ctx.fillStyle = '#FFFFFF';
            this.ctx.font = `${size * 0.3}px Arial`;
            this.ctx.textAlign = 'center';
            this.ctx.fillText(block.type, x + size / 2, y + size / 2 + 5);
        }
    }
    
    // 获取方块颜色
    getBlockColor(type) {
        const colors = {
            'cat': '#FF6B9D',
            'dog': '#4ECDC4',
            'elephant': '#45B7D1',
            'fox': '#96CEB4',
            'frog': '#FFEAA7',
            'monkey': '#DDA0DD',
            'panda': '#98D8C8',
            'rabbit': '#F7DC6F',
            'tiger': '#BB8FCE',
            'rocket': '#FF4500',
            'bomb': '#8B0000'
        };
        return colors[type] || '#CCCCCC';
    }
    
    // 渲染道具栏
    renderPropBar() {
        const config = this.uiConfig.getPropBarConfig();
        
        // 绘制背景
        this.ctx.fillStyle = config.backgroundColor;
        this.ctx.beginPath();
        this.ctx.roundRect(config.x, config.y, config.width, config.height, config.borderRadius);
        this.ctx.fill();
        
        // 绘制边框
        this.ctx.strokeStyle = config.borderColor;
        this.ctx.lineWidth = 2;
        this.ctx.stroke();
        
        // 绘制道具按钮
        const buttonWidth = 60;
        const buttonSpacing = 20;
        const startX = config.x + 20;
        
        ['refresh', 'bomb', 'clear'].forEach((prop, index) => {
            const buttonX = startX + index * (buttonWidth + buttonSpacing);
            const buttonY = config.y + 10;
            
            // 绘制按钮背景
            this.ctx.fillStyle = this.uiConfig.colors.secondary;
            this.ctx.beginPath();
            this.ctx.roundRect(buttonX, buttonY, buttonWidth, 60, 10);
            this.ctx.fill();
            
            // 绘制按钮文字
            this.ctx.fillStyle = this.uiConfig.colors.text;
            this.ctx.font = '14px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillText(prop, buttonX + buttonWidth / 2, buttonY + 35);
        });
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SimpleGamePageRenderer;
} else if (typeof window !== 'undefined') {
    window.SimpleGamePageRenderer = SimpleGamePageRenderer;
}
