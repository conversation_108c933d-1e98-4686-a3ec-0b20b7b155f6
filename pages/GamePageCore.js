// GamePage核心类 - 基础功能和初始化（超美化版本）
class GamePageCore {
    
    // 静态布局配置 - 统一管理所有界面坐标
    static LAYOUT = {
        // 网格配置
        GRID_SIZE_X: 8,
        GRID_SIZE_Y: 10,
        BLOCK_SIZE: 45,
        
        // 网格位置
        GRID_START_Y: 300,  // 网格起始Y坐标（向上移动30像素）
        GRID_MARGIN_TOP: 50, // 网格顶部边距
        
        // UI元素位置
        UI_OFFSET_Y: 200,    // UI整体向下偏移量
        HEADER_HEIGHT: 120,  // 顶部区域高度
        SCORE_AREA_Y: 220,   // 分数区域Y坐标（20 + 200）
        LEVEL_INFO_Y: 260,   // 关卡信息Y坐标（60 + 200）
        PROGRESS_BAR_Y: 290, // 进度条Y坐标（90 + 200）
        
        // 道具区域
        PROPS_AREA_Y: 100,   // 道具区域Y坐标
        PROPS_BUTTON_SIZE: 60, // 道具按钮大小
        PROPS_SPACING: 80,   // 道具按钮间距
        
        // 按钮区域
        BACK_BUTTON_Y: 70,   // 返回按钮Y坐标（200 + 50 - 180 = 70）
        BACK_BUTTON_HEIGHT: 32, // 返回按钮高度（调小）
        BOTTOM_AREA_Y: 750,  // 底部区域Y坐标
        BUTTON_HEIGHT: 50,   // 其他按钮高度
        BUTTON_SPACING: 20,  // 按钮间距
        
        // 弹窗配置
        DIALOG_WIDTH: 400,   // 弹窗宽度
        DIALOG_HEIGHT: 300,  // 弹窗高度
        
        // 动画配置
        PARTICLE_COUNT: 8,   // 粒子数量
        SPARKLE_COUNT: 3,    // 闪烁效果数量
        ANIMATION_SPEED: 0.016, // 动画速度 (60fps)
        
        // 颜色配置
        COLORS: {
            BACKGROUND: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            GRID_BACKGROUND: 'rgba(255, 255, 255, 0.1)',
            GRID_BORDER: 'rgba(255, 255, 255, 0.3)',
            TEXT_PRIMARY: '#FFFFFF',
            TEXT_SECONDARY: '#E0E0E0',
            BUTTON_PRIMARY: '#4CAF50',
            BUTTON_SECONDARY: '#2196F3',
            BUTTON_DANGER: '#F44336'
        }
    };
    
    constructor(gameManager, levelConfig) {
        this.gameManager = gameManager;
        this.canvas = gameManager.canvas;
        this.ctx = gameManager.ctx;

        // 初始化UI配置
        if (typeof GameUIConfig !== 'undefined') {
            this.uiConfig = new GameUIConfig(this.canvas.width, this.canvas.height);
        } else {
            console.error('GameUIConfig 未定义，请确保已加载 GameUIConfig.js');
            // 使用默认配置作为后备方案
            this.uiConfig = this.createDefaultUIConfig();
        }

        // 设置关卡配置
        this.levelConfig = levelConfig || {
            targetScore: 1000,
            name: '萌宠新手村',
            level: 1
        };

        // 初始化游戏数据
        this.score = 0;

        // 从配置中获取游戏参数
        this.targetScore = this.levelConfig.targetScore;
        this.levelName = this.levelConfig.name;
        this.level = this.levelConfig.level;

        // 将分数和进度设为全局变量方便直接计算和赋值
        window.gameScore = this.score;
        window.gameTargetScore = this.targetScore;
        window.gameProgress = 0;

        // 网格配置（使用新的UI配置）
        const gridConfig = this.uiConfig.getGridConfig();
        this.gridSizeX = gridConfig.cols;
        this.gridSizeY = gridConfig.rows;
        this.blockSize = gridConfig.blockSize;
        // 注意：gridStartX 和 gridStartY 是 getter 属性，不需要设置
        
        // 根据关卡配置萌宠类型
        this.initAnimalTypes();
        this.animalImages = {};
        this.propImages = {};

        // 萌宠颜色配置
        this.animalColors = {
            'cat': '#FF6B9D',     // 粉色猫咪
            'dog': '#4ECDC4',     // 青色小狗
            'elephant': '#45B7D1', // 蓝色大象
            'fox': '#96CEB4',     // 绿色狐狸
            'frog': '#FFEAA7',    // 黄色青蛙
            'monkey': '#DDA0DD',  // 紫色猴子
            'panda': '#98D8C8',   // 薄荷绿熊猫
            'rabbit': '#F7DC6F',  // 金色兔子
            'tiger': '#BB8FCE'    // 淡紫色老虎
        };

        // 特殊方块类型
        this.specialTypes = {
            ROCKET: 'rocket',
            BOMB: 'bomb'
        };

        // 特殊方块颜色
        this.specialColors = {
            'rocket': '#FF4500',  // 橙红色火箭
            'bomb': '#8B0000'     // 深红色炸弹
        };
        
        // 拖拽相关状态
        this.isDragging = false;
        this.dragStartRow = -1;
        this.dragStartCol = -1;
        this.dragEndRow = -1;
        this.dragEndCol = -1;
        this.dragStartX = -1;
        this.dragStartY = -1;
        this.dragCurrentX = -1;
        this.dragCurrentY = -1;
        
        // 游戏状态
        this.score = 0;
        this.isAnimating = false;
        this.selectedBlock = null;
        this.isGameOver = false;
        this.isLevelComplete = false;
        this.showLevelCompleteUI = false;
        this.reviveCount = 0;
        this.isBombCardSelecting = false;
        this.showExitDialog = false;
        
        // 计分规则
        this.scoreRules = {
            match3: 20,
            match4: 30,
            match5: 50,
            rocketCombo: 100,
            bombCombo: 150,
            rocketBombCombo: 300,
            bombBombCombo: 500,
            comboMultipliers: [1.0, 1.5, 2.0] // 1次、2次、3次及以上连击倍率
        };

        // 连击系统
        this.combo = 0;
        this.maxCombo = 0;
        this.comboMultiplier = 1.0;
        this.comboCount = 0; // 连续消除次数
        this.comboTimer = 0; // 连击计时器
        
        // 动画和效果
        this.fallingBlocks = [];
        this.particles = [];
        this.sparkles = [];
        this.floatingTexts = [];
        this.animations = [];
        this.animationTime = 0;
        
        // 道具系统 - 从UI配置中获取初始数量
        this.props = {
            refresh: this.uiConfig ? this.uiConfig.getPropInitialCount('refresh') : 3,
            bomb: this.uiConfig ? this.uiConfig.getPropInitialCount('bomb') : 2,
            clear: this.uiConfig ? this.uiConfig.getPropInitialCount('clear') : 1
        };

        // 道具使用状态
        this.propUsing = {
            type: null,     // 当前使用的道具类型
            isActive: false // 是否正在使用道具
        };
        
        // 音效管理器
        this.audioManager = gameManager.audioManager;
        
        // 背景效果
        this.backgroundStars = null;
        
        // 动画控制器
        this.animator = null;

        // 事件处理器
        this.events = null;

        console.log('GamePageCore初始化完成');
    }

    /**
     * 更新分数并同步全局变量
     * @param {number} points - 要增加的分数
     */
    updateScore(points) {
        this.score += points;
        window.gameScore = this.score;
        window.gameProgress = Math.min(this.score / this.targetScore, 1.0);
        console.log(`分数更新: +${points}, 总分: ${this.score}, 进度: ${(window.gameProgress * 100).toFixed(1)}%`);

        // 检查是否达到通关条件
        this.checkGameStatus();
    }

    /**
     * 重置分数并同步全局变量
     */
    resetScore() {
        this.score = 0;
        window.gameScore = this.score;
        window.gameProgress = 0;
        console.log('分数已重置');
    }

    // 根据关卡初始化萌宠类型
    initAnimalTypes() {
        const allAnimals = ['cat', 'dog', 'elephant', 'fox', 'frog', 'monkey', 'panda', 'rabbit', 'tiger'];

        // 根据关卡确定萌宠数量
        let animalCount;
        switch (this.level) {
            case 1:
                animalCount = 5; // 第一关5种萌宠
                break;
            case 2:
                animalCount = 7; // 第二关7种萌宠
                break;
            case 3:
            default:
                animalCount = 9; // 第三关及以上9种萌宠
                break;
        }

        // 随机选择指定数量的萌宠类型
        this.animalTypes = allAnimals.slice(0, animalCount);
        console.log(`关卡${this.level}使用${animalCount}种萌宠:`, this.animalTypes);
    }
    
    // 初始化游戏网格
    initGrid() {
        console.log('开始初始化游戏网格');
        
        this.grid = [];
        const startX = this.gridStartX;
        const startY = this.gridStartY;
        
        for (let row = 0; row < this.gridSizeY; row++) {
            this.grid[row] = [];
            for (let col = 0; col < this.gridSizeX; col++) {
                const block = this.createRandomBlock(row, col, startX, startY);
                this.grid[row][col] = block;
            }
        }
        
        console.log('游戏网格初始化完成');
        this.removeInitialMatches();
    }
    
    // 创建随机方块（增强版本）
    createRandomBlock(row = 0, col = 0, startX = 0, startY = 0, forceType = null) {
        if (!this.animalTypes || this.animalTypes.length === 0) {
            console.error('animalTypes未初始化');
            return null;
        }

        let type, color, blockType;

        if (forceType) {
            // 强制指定类型（用于生成特殊方块）
            type = forceType;
            if (forceType === 'rocket' || forceType === 'bomb') {
                blockType = 'special';
                color = this.specialColors[forceType] || '#FF4500';
            } else {
                blockType = 'normal';
                color = this.animalColors[forceType] || '#FF6B9D';
            }
        } else {
            // 普通随机生成
            type = this.animalTypes[Math.floor(Math.random() * this.animalTypes.length)];
            blockType = 'normal';
            color = this.animalColors[type] || '#FF6B9D';
        }

        return {
            type: type,
            color: color,
            blockType: blockType, // 'normal' 或 'special'
            special: blockType === 'special' ? type : null,

            // 位置信息（考虑格子间距）
            x: startX + col * (this.blockSize + this.getGridSpacing()),
            y: startY + row * (this.blockSize + this.getGridSpacing()),
            row: row,
            col: col,

            // 动画属性
            scale: 1,
            alpha: 1,
            rotation: 0,
            animationOffset: Math.random() * Math.PI * 2,

            // 状态
            isSelected: false,
            isMatched: false,
            isFalling: false,

            // 特效
            glowIntensity: 0,
            pulsePhase: Math.random() * Math.PI * 2
        };
    }

    // 创建特殊方块
    createSpecialBlock(row, col, startX, startY, specialType) {
        return this.createRandomBlock(row, col, startX, startY, specialType);
    }
    
    // 移除初始匹配 - 增强版，确保不出现3个连在一起
    removeInitialMatches() {
        let hasMatches = true;
        let attempts = 0;
        const maxAttempts = 100; // 增加最大尝试次数

        while (hasMatches && attempts < maxAttempts) {
            const matchResult = this.checkForMatches();
            if (!matchResult || matchResult.matches.length === 0) {
                hasMatches = false;
            } else {
                // 替换匹配的方块
                matchResult.matches.forEach(match => {
                    let newType;
                    let validType = false;
                    let typeAttempts = 0;

                    // 尝试找到一个不会产生新匹配的类型
                    while (!validType && typeAttempts < 20) {
                        newType = this.animalTypes[Math.floor(Math.random() * this.animalTypes.length)];

                        // 临时设置新类型
                        const originalType = this.grid[match.row][match.col].type;
                        this.grid[match.row][match.col].type = newType;
                        this.grid[match.row][match.col].color = this.animalColors[newType];

                        // 检查是否还会产生匹配
                        if (!this.wouldCreateMatch(match.row, match.col, newType)) {
                            validType = true;
                        } else {
                            // 恢复原类型继续尝试
                            this.grid[match.row][match.col].type = originalType;
                            typeAttempts++;
                        }
                    }

                    // 如果找不到合适的类型，使用随机类型
                    if (!validType) {
                        const newBlock = this.createRandomBlock(
                            match.row,
                            match.col,
                            this.gridStartX,
                            this.gridStartY
                        );
                        this.grid[match.row][match.col] = newBlock;
                    }
                });
            }
            attempts++;
        }

        if (attempts >= maxAttempts) {
            console.warn('移除初始匹配达到最大尝试次数');
        }

        console.log(`初始化完成，尝试${attempts}次移除匹配`);
    }

    // 检查指定位置放置指定类型是否会产生匹配
    wouldCreateMatch(row, col, type) {
        // 检查水平方向
        let horizontalCount = 1;

        // 向左检查
        for (let c = col - 1; c >= 0; c--) {
            if (this.grid[row][c] && this.grid[row][c].type === type && this.grid[row][c].blockType === 'normal') {
                horizontalCount++;
            } else {
                break;
            }
        }

        // 向右检查
        for (let c = col + 1; c < this.gridSizeX; c++) {
            if (this.grid[row][c] && this.grid[row][c].type === type && this.grid[row][c].blockType === 'normal') {
                horizontalCount++;
            } else {
                break;
            }
        }

        if (horizontalCount >= 3) return true;

        // 检查垂直方向
        let verticalCount = 1;

        // 向上检查
        for (let r = row - 1; r >= 0; r--) {
            if (this.grid[r][col] && this.grid[r][col].type === type && this.grid[r][col].blockType === 'normal') {
                verticalCount++;
            } else {
                break;
            }
        }

        // 向下检查
        for (let r = row + 1; r < this.gridSizeY; r++) {
            if (this.grid[r][col] && this.grid[r][col].type === type && this.grid[r][col].blockType === 'normal') {
                verticalCount++;
            } else {
                break;
            }
        }

        if (verticalCount >= 3) return true;

        return false;
    }

    // 道具系统方法

    // 使用刷新卡
    useRefreshProp() {
        if (this.props.refresh <= 0) {
            console.log('刷新卡数量不足');
            return false;
        }

        this.props.refresh--;

        // 添加刷新动画效果
        this.startRefreshAnimation();

        console.log('使用刷新卡，剩余数量:', this.props.refresh);
        return true;
    }

    // 开始刷新动画
    startRefreshAnimation() {
        console.log('开始刷新动画');

        // 第一阶段：所有方块缩小并旋转
        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX; col++) {
                const block = this.grid[row][col];
                if (block) {
                    // 添加缩小和旋转动画
                    block.isRefreshing = true;
                    block.refreshPhase = 'shrinking';
                    block.refreshStartTime = Date.now();
                    block.originalScale = block.scale || 1;
                    block.originalRotation = block.rotation || 0;
                }
            }
        }

        // 设置刷新状态
        this.isRefreshing = true;
        this.refreshStartTime = Date.now();

        // 1秒后执行实际的打乱逻辑
        setTimeout(() => {
            this.executeRefreshShuffle();
        }, 1000);
    }

    // 执行刷新打乱
    executeRefreshShuffle() {
        console.log('执行刷新打乱');

        // 使用打乱逻辑而不是重新生成
        this.shuffleGrid();

        // 确保没有初始匹配
        this.removeInitialMatches();

        // 第二阶段：所有方块放大并恢复
        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX; col++) {
                const block = this.grid[row][col];
                if (block) {
                    // 添加放大动画
                    block.refreshPhase = 'expanding';
                    block.refreshStartTime = Date.now();
                }
            }
        }

        // 1秒后结束刷新动画
        setTimeout(() => {
            this.endRefreshAnimation();
        }, 1000);
    }

    // 结束刷新动画
    endRefreshAnimation() {
        console.log('结束刷新动画');

        // 重置所有方块的刷新状态
        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX; col++) {
                const block = this.grid[row][col];
                if (block) {
                    block.isRefreshing = false;
                    block.refreshPhase = null;
                    block.refreshStartTime = null;
                    block.scale = 1;
                    block.rotation = 0;
                }
            }
        }

        this.isRefreshing = false;
        this.refreshStartTime = null;
    }

    // 使用清屏卡
    useClearProp() {
        if (this.props.clear <= 0) {
            console.log('清屏卡数量不足');
            return false;
        }

        this.props.clear--;

        // 清空所有网格
        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX; col++) {
                const block = this.grid[row][col];
                if (block) {
                    // 添加消失特效
                    this.addParticleEffect(block.x + this.blockSize / 2, block.y + this.blockSize / 2, block.color);
                }
                this.grid[row][col] = null;
            }
        }

        // 增加分数
        this.updateScore(800);
        this.addFloatingText('+800', this.canvas.width / 2, this.canvas.height / 2, '#FFD700');

        // 重新填充网格（清屏后的特殊填充）
        setTimeout(() => {
            this.refillGridAfterClear();
        }, 500);

        console.log('使用清屏卡，剩余数量:', this.props.clear);
        return true;
    }

    // 激活炸弹卡（需要用户选择位置）
    activateBombProp() {
        if (this.props.bomb <= 0) {
            console.log('炸弹卡数量不足');
            return false;
        }

        this.propUsing.type = 'bomb';
        this.propUsing.isActive = true;

        console.log('炸弹卡已激活，请选择爆炸位置');
        return true;
    }

    // 使用炸弹卡在指定位置
    useBombPropAt(row, col) {
        console.log(`尝试使用炸弹卡: 位置(${row}, ${col}), 激活状态: ${this.propUsing.isActive}, 类型: ${this.propUsing.type}, 数量: ${this.props.bomb}`);

        if (!this.propUsing.isActive || this.propUsing.type !== 'bomb') {
            console.log('炸弹卡使用失败: 道具未激活或类型不匹配');
            return false;
        }

        if (this.props.bomb <= 0) {
            console.log('炸弹卡使用失败: 数量不足');
            return false;
        }

        this.props.bomb--;
        this.propUsing.isActive = false;
        this.propUsing.type = null;

        console.log(`炸弹卡使用成功: 位置(${row}, ${col}), 剩余数量: ${this.props.bomb}`);

        // 5x5范围爆炸
        const eliminatedBlocks = this.eliminateArea(row, col, 5);

        // 增加分数
        this.updateScore(500);
        this.addFloatingText('+500', col * this.blockSize + this.gridStartX + this.blockSize/2,
                           row * this.blockSize + this.gridStartY + this.blockSize/2, '#FFD700');

        // 播放爆炸音效和特效
        if (this.gameManager.audioManager) {
            this.gameManager.audioManager.playSound('bomb');
        }
        this.addExplosionEffect(col * this.blockSize + this.gridStartX + this.blockSize/2,
                              row * this.blockSize + this.gridStartY + this.blockSize/2, true);

        // 清除被消除的方块
        eliminatedBlocks.forEach(item => {
            this.grid[item.row][item.col] = null;
        });

        // 处理掉落（炸弹后的下落）
        setTimeout(() => {
            if (this.animator && this.animator.falling) {
                const removedPositions = eliminatedBlocks.map(item => ({ row: item.row, col: item.col }));
                this.animator.falling.processFalling(removedPositions, 'bomb');
            }
        }, 300);

        console.log('使用炸弹卡，剩余数量:', this.props.bomb);
        return true;
    }

    // 重新填充网格
    refillGrid() {
        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX; col++) {
                if (!this.grid[row][col]) {
                    const newBlock = this.createRandomBlock(row, col, this.gridStartX, this.gridStartY);
                    this.grid[row][col] = newBlock;
                }
            }
        }

        // 确保没有初始匹配
        this.removeInitialMatches();
    }

    // 清屏后的特殊填充逻辑
    refillGridAfterClear() {
        console.log('开始清屏后的网格填充');

        // 获取动画配置
        const animConfig = this.uiConfig ? this.uiConfig.getAnimationConfig() : { clearFallSpeed: 6 };
        const fallSpeed = animConfig.clearFallSpeed;

        // 按列处理，优先填充最下方
        for (let col = 0; col < this.gridSizeX; col++) {
            this.fillColumnAfterClear(col, fallSpeed);
        }

        // 确保没有初始匹配
        setTimeout(() => {
            this.removeInitialMatches();
        }, 2000); // 等待所有方块下落完成
    }

    // 清屏后填充单列
    fillColumnAfterClear(col, fallSpeed) {
        const gridConfig = this.uiConfig ? this.uiConfig.getGridConfig() : null;
        const blockSize = gridConfig ? gridConfig.blockSize : this.blockSize;
        const startX = gridConfig ? gridConfig.startX : this.gridStartX;
        const startY = gridConfig ? gridConfig.startY : this.gridStartY;
        const padding = gridConfig ? gridConfig.padding : 0;

        // 从最下方开始填充，每个方块从第一排开始下落
        for (let targetRow = this.gridSizeY - 1; targetRow >= 0; targetRow--) {
            // 计算延迟时间，让方块依次下落
            const delay = (this.gridSizeY - 1 - targetRow) * 100; // 每个方块间隔100ms

            setTimeout(() => {
                // 创建新方块，初始位置在第一排上方
                const startRow = -1; // 从网格上方开始
                const newBlock = this.createRandomBlock(startRow, col, startX, startY);

                // 设置方块的初始位置（在网格上方）
                newBlock.x = startX + col * (blockSize + padding);
                newBlock.y = startY - blockSize; // 在网格上方
                newBlock.isFalling = true;
                newBlock.fallSpeed = fallSpeed;
                newBlock.targetRow = targetRow;
                newBlock.targetCol = col;
                newBlock.fallType = 'clear'; // 标记为清屏后的下落

                // 计算目标位置
                newBlock.targetX = startX + col * (blockSize + padding);
                newBlock.targetY = startY + targetRow * (blockSize + padding);

                console.log(`创建清屏填充方块: 列${col}, 目标行${targetRow}, 下落速度${fallSpeed}`);

                // 启动下落动画
                this.animateClearFall(newBlock);

            }, delay);
        }
    }

    // 清屏后的下落动画
    animateClearFall(block) {
        const animate = () => {
            if (!block.isFalling) return;

            // 向下移动
            block.y += block.fallSpeed;

            // 检查是否到达目标位置
            if (block.y >= block.targetY) {
                // 到达目标位置
                block.y = block.targetY;
                block.x = block.targetX;
                block.isFalling = false;

                // 将方块放置到网格中
                this.grid[block.targetRow][block.targetCol] = block;

                console.log(`清屏填充方块到达目标位置: (${block.targetRow}, ${block.targetCol})`);

                // 检查是否所有方块都已到位
                this.checkClearFillComplete();
            } else {
                // 继续动画
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    }

    // 检查清屏填充是否完成
    checkClearFillComplete() {
        let allFilled = true;

        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX; col++) {
                if (!this.grid[row][col]) {
                    allFilled = false;
                    break;
                }
            }
            if (!allFilled) break;
        }

        if (allFilled) {
            console.log('清屏后网格填充完成');
            // 可以在这里添加完成后的逻辑
        }
    }
    
    // 初始化背景效果
    initBackgroundEffects() {
        try {
            if (typeof BackgroundUtils !== 'undefined') {
                this.backgroundStars = BackgroundUtils.createStars(15, this.canvas.width, this.canvas.height);
            } else {
                console.warn('BackgroundUtils未加载，使用简化背景');
                this.backgroundStars = null;
            }
        } catch (error) {
            console.warn('背景效果初始化失败:', error);
            this.backgroundStars = null;
        }
    }
    
    // 加载萌宠图片
    loadAnimalImages() {
        console.log('开始加载萌宠图片...');

        // 获取图片配置
        const imageConfig = this.uiConfig ? this.uiConfig.getImageConfig() : null;

        // 加载萌宠图片
        this.animalTypes.forEach(type => {
            const img = new Image();
            // 使用配置中的路径
            if (imageConfig) {
                img.src = this.uiConfig.getAnimalImagePath(type);
            } else {
                // 后备方案
                img.src = `images/animal/${type}.png`;
            }

            img.onload = () => {
                console.log(`萌宠${type}图片加载完成`);
            };
            img.onerror = () => {
                console.warn(`萌宠${type}图片加载失败，使用默认颜色`);
            };
            this.animalImages[type] = img;
        });

        // 加载特殊方块图片
        const specialTypes = ['rocket', 'bomb'];
        specialTypes.forEach(type => {
            const img = new Image();
            // 使用配置中的路径
            if (imageConfig) {
                img.src = this.uiConfig.getExtraImagePath(type);
            } else {
                // 后备方案
                img.src = `images/extra/${type}.png`;
            }

            img.onload = () => {
                console.log(`特殊方块${type}图片加载完成`);
            };
            img.onerror = () => {
                console.warn(`特殊方块${type}图片加载失败，使用默认图标`);
            };
            this.propImages[type] = img;

            // 为了兼容性，也添加到特殊方块图片映射中
            if (type === 'bomb') {
                this.propImages['bomb_extra'] = img;
            }
        });

        // 加载道具图片
        const propTypes = ['refresh', 'bomb', 'clear'];
        propTypes.forEach(type => {
            const img = new Image();
            // 使用配置中的路径
            if (imageConfig) {
                img.src = this.uiConfig.getPropImagePath(type);
            } else {
                // 后备方案
                img.src = `images/prop/${type}.png`;
            }

            img.onload = () => {
                console.log(`道具${type}图片加载完成`);
            };
            img.onerror = () => {
                console.warn(`道具${type}图片加载失败，使用默认图标`);
            };
            this.propImages[type] = img;
        });
    }
    
    // 检查匹配（增强版本）- 支持3连消、4连消、5连消
    checkForMatches() {
        const matches = [];
        const visited = new Set();
        const matchGroups = []; // 存储匹配组信息

        // 检查水平匹配
        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX - 2; col++) {
                const block = this.grid[row][col];
                if (!block || block.blockType !== 'normal' || !block.type) {
                    console.log(`跳过方块 (${row},${col}): ${!block ? '空' : block.blockType !== 'normal' ? '非普通' : '无type'}`);
                    continue;
                }

                const type = block.type;
                const block1 = this.grid[row][col + 1];
                const block2 = this.grid[row][col + 2];

                if (block1 && block1.type === type && block1.blockType === 'normal' &&
                    block2 && block2.type === type && block2.blockType === 'normal') {

                    let count = 3;
                    let endCol = col + 2;

                    // 扩展匹配
                    while (endCol + 1 < this.gridSizeX) {
                        const nextBlock = this.grid[row][endCol + 1];
                        if (nextBlock && nextBlock.type === type && nextBlock.blockType === 'normal') {
                            count++;
                            endCol++;
                        } else {
                            break;
                        }
                    }

                    // 记录匹配组
                    const matchGroup = {
                        type: 'horizontal',
                        count: count,
                        blocks: []
                    };

                    // 添加匹配的方块
                    for (let i = 0; i < count; i++) {
                        const key = `${row}-${col + i}`;
                        if (!visited.has(key)) {
                            const matchBlock = { row, col: col + i, type, count, matchType: 'horizontal' };
                            matches.push(matchBlock);
                            matchGroup.blocks.push(matchBlock);
                            visited.add(key);
                        }
                    }

                    matchGroups.push(matchGroup);
                    console.log(`检测到水平${count}连消: 行${row}, 列${col}-${endCol}, 类型${type}`);
                    col = endCol;
                }
            }
        }

        // 检查垂直匹配
        for (let col = 0; col < this.gridSizeX; col++) {
            for (let row = 0; row < this.gridSizeY - 2; row++) {
                const block = this.grid[row][col];
                if (!block || block.blockType !== 'normal' || !block.type) {
                    console.log(`跳过方块 (${row},${col}): ${!block ? '空' : block.blockType !== 'normal' ? '非普通' : '无type'}`);
                    continue;
                }

                const type = block.type;
                const block1 = this.grid[row + 1][col];
                const block2 = this.grid[row + 2][col];

                if (block1 && block1.type === type && block1.blockType === 'normal' &&
                    block2 && block2.type === type && block2.blockType === 'normal') {

                    let count = 3;
                    let endRow = row + 2;

                    // 扩展匹配
                    while (endRow + 1 < this.gridSizeY) {
                        const nextBlock = this.grid[endRow + 1][col];
                        if (nextBlock && nextBlock.type === type && nextBlock.blockType === 'normal') {
                            count++;
                            endRow++;
                        } else {
                            break;
                        }
                    }

                    // 记录匹配组
                    const matchGroup = {
                        type: 'vertical',
                        count: count,
                        blocks: []
                    };

                    // 添加匹配的方块
                    for (let i = 0; i < count; i++) {
                        const key = `${row + i}-${col}`;
                        if (!visited.has(key)) {
                            const matchBlock = { row: row + i, col, type, count, matchType: 'vertical' };
                            matches.push(matchBlock);
                            matchGroup.blocks.push(matchBlock);
                            visited.add(key);
                        }
                    }

                    matchGroups.push(matchGroup);
                    console.log(`检测到垂直${count}连消: 列${col}, 行${row}-${endRow}, 类型${type}`);
                    row = endRow;
                }
            }
        }

        return { matches, matchGroups };
    }
    
    // 处理匹配消除 - 支持新计分规则和特殊方块生成
    processMatches(matchResult) {
        if (!matchResult || matchResult.matches.length === 0) {
            // 没有匹配时重置连击
            this.resetCombo();
            return { score: 0, specialBlocks: [] };
        }

        const { matches, matchGroups } = matchResult;
        let totalScore = 0;
        const specialBlocks = [];

        // 增加连击数
        this.comboCount++;
        this.maxCombo = Math.max(this.maxCombo, this.comboCount);

        // 计算连击倍率 - 使用配置中的倍率
        if (this.uiConfig) {
            this.comboMultiplier = this.uiConfig.getComboScoreMultiplier(this.comboCount);
        } else {
            // 后备方案
            const comboIndex = Math.min(this.comboCount - 1, this.scoreRules.comboMultipliers.length - 1);
            this.comboMultiplier = this.scoreRules.comboMultipliers[comboIndex];
        }

        // 播放消除音效
        if (this.gameManager.audioManager) {
            this.gameManager.audioManager.playSound('so');
        }

        // 处理每个匹配组
        matchGroups.forEach((group, index) => {
            // 验证匹配组数据完整性
            if (!group || (!group.blocks && !group.length)) {
                console.error(`匹配组${index + 1}数据无效:`, group);
                return;
            }

            let baseScore;
            // 获取匹配数量，兼容不同的数据结构
            const count = group.count || group.length || (group.blocks ? group.blocks.length : 0);
            console.log(`处理匹配组${index + 1}: 类型${group.type}, 数量${count}, 结构:`, group);

            // 根据消除数量计算基础分数
            if (count === 3) {
                baseScore = this.scoreRules.match3;
            } else if (count === 4) {
                baseScore = this.scoreRules.match4;
                // 4连消生成火箭
                const blocks = group.blocks || [group];
                if (Array.isArray(blocks) && blocks.length > 0) {
                    const centerBlock = blocks[Math.floor(blocks.length / 2)];
                    specialBlocks.push({
                        row: centerBlock.row,
                        col: centerBlock.col,
                        type: 'rocket'
                    });
                    console.log(`4连消生成火箭: 位置(${centerBlock.row}, ${centerBlock.col})`);
                }
            } else if (count >= 5) {
                baseScore = this.scoreRules.match5;
                // 5连消生成炸弹
                const blocks = group.blocks || [group];
                if (Array.isArray(blocks) && blocks.length > 0) {
                    const centerBlock = blocks[Math.floor(blocks.length / 2)];
                    specialBlocks.push({
                        row: centerBlock.row,
                        col: centerBlock.col,
                        type: 'bomb'
                    });
                    console.log(`5连消生成炸弹: 位置(${centerBlock.row}, ${centerBlock.col})`);
                }
            } else {
                // 默认分数，防止undefined
                baseScore = this.scoreRules.match3;
                console.warn(`未知的匹配数量: ${count}, 使用默认分数`);
            }

            const finalScore = Math.floor(baseScore * this.comboMultiplier);
            totalScore += finalScore;
            console.log(`消除组得分: 基础${baseScore} x 倍率${this.comboMultiplier} = ${finalScore}, 累计${totalScore}`);

            // 为每个方块添加特效
            const blocks = group.blocks || [group]; // 兼容不同的数据结构
            if (Array.isArray(blocks)) {
                blocks.forEach(match => {
                    const block = this.grid[match.row][match.col];
                    if (block) {
                        // 标记为已匹配
                        block.isMatched = true;

                        // 添加粒子效果
                        this.addParticleEffect(block.x + this.blockSize / 2, block.y + this.blockSize / 2, block.color);
                    }
                });
            } else {
                console.error('group.blocks不是数组:', blocks, '完整group:', group);
            }

            // 添加浮动分数文字 - 显示计算了连击倍率后的最终分数
            const blocksForText = group.blocks || [group];
            if (Array.isArray(blocksForText) && blocksForText.length > 0) {
                const centerBlock = blocksForText[Math.floor(blocksForText.length / 2)];
                const block = this.grid[centerBlock.row][centerBlock.col];
                if (block) {
                    // 显示最终分数（包含连击倍率）
                    this.addFloatingText(`+${finalScore}`, block.x + this.blockSize / 2, block.y + this.blockSize / 2, '#FFD700');
                }
            }
        });

        // 添加连击显示和音效
        if (this.comboCount > 1) {
            this.addComboText(this.comboCount, this.comboMultiplier);

            // 播放连击音效 - 使用配置中的音效触发规则
            if (this.gameManager.audioManager && this.uiConfig) {
                const audioName = this.uiConfig.getComboAudio(this.comboCount);
                if (audioName) {
                    this.gameManager.audioManager.playSound(audioName);
                    console.log(`播放${this.comboCount}连击音效: ${audioName}.mp3`);
                }
            } else if (this.gameManager.audioManager) {
                // 后备方案：使用原有的音效逻辑
                if (this.comboCount === 2) {
                    this.gameManager.audioManager.playSound('wa');
                    console.log('播放2连击音效: wa.mp3');
                } else if (this.comboCount === 4) {
                    this.gameManager.audioManager.playSound('good');
                    console.log('播放4连击音效: good.mp3');
                }
            }
        }

        // 检查是否还有可能的消除
        setTimeout(() => {
            this.checkForPossibleMoves();
        }, 500);

        return { score: totalScore, specialBlocks };
    }

    // 重置连击
    resetCombo() {
        this.comboCount = 0;
        this.comboMultiplier = 1.0;
        this.comboTimer = 0;
        console.log('连击重置');
    }

    // 检查是否还有可能的移动
    checkForPossibleMoves() {
        if (this.isGameOver || this.isLevelComplete || this.isAnimating) {
            return;
        }

        console.log('检查可能的移动...');

        // 检查所有相邻位置的交换是否能产生匹配
        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX; col++) {
                const block = this.grid[row][col];
                if (!block) continue;

                // 检查右边的交换
                if (col < this.gridSizeX - 1) {
                    const rightBlock = this.grid[row][col + 1];
                    if (rightBlock && this.canSwapAndMatch(row, col, row, col + 1)) {
                        console.log(`找到可能的移动: (${row},${col}) <-> (${row},${col + 1})`);
                        return; // 找到可能的移动，游戏继续
                    }
                }

                // 检查下边的交换
                if (row < this.gridSizeY - 1) {
                    const bottomBlock = this.grid[row + 1][col];
                    if (bottomBlock && this.canSwapAndMatch(row, col, row + 1, col)) {
                        console.log(`找到可能的移动: (${row},${col}) <-> (${row + 1},${col})`);
                        return; // 找到可能的移动，游戏继续
                    }
                }
            }
        }

        // 没有找到可能的移动，游戏失败
        console.log('没有找到可能的移动，游戏失败');
        this.triggerGameOver();
    }

    // 检查交换后是否能产生匹配
    canSwapAndMatch(row1, col1, row2, col2) {
        // 临时交换
        const temp = this.grid[row1][col1];
        this.grid[row1][col1] = this.grid[row2][col2];
        this.grid[row2][col2] = temp;

        // 检查是否有匹配
        const matchResult = this.checkForMatches();
        const hasMatch = matchResult && matchResult.matches.length > 0;

        // 交换回来
        this.grid[row2][col2] = this.grid[row1][col1];
        this.grid[row1][col1] = temp;

        return hasMatch;
    }

    // 触发游戏失败
    triggerGameOver() {
        this.isGameOver = true;

        // 播放失败音效
        if (this.gameManager.audioManager) {
            this.gameManager.audioManager.playSound('lose');
        }

        // 显示失败对话框
        setTimeout(() => {
            this.showGameOverDialog();
        }, 500);

        console.log('游戏失败');
    }

    // 显示游戏失败对话框
    showGameOverDialog() {
        this.showGameOverDialog = true;
        console.log('显示游戏失败对话框');
    }

    // 复活游戏（触发刷新卡效果但不消耗刷新卡）
    reviveGame() {
        console.log('复活游戏');
        this.isGameOver = false;
        this.showGameOverDialog = false;

        // 触发刷新卡效果但不消耗数量
        this.shuffleGrid();

        // 确保没有初始匹配
        this.removeInitialMatches();

        console.log('游戏复活成功');
    }

    // 打乱网格（刷新卡效果）
    shuffleGrid() {
        const blocks = [];

        // 收集所有方块
        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX; col++) {
                if (this.grid[row][col]) {
                    blocks.push(this.grid[row][col]);
                }
            }
        }

        // 打乱数组
        for (let i = blocks.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [blocks[i], blocks[j]] = [blocks[j], blocks[i]];
        }

        // 重新分配到网格
        let blockIndex = 0;
        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX; col++) {
                if (blockIndex < blocks.length) {
                    const block = blocks[blockIndex];
                    block.row = row;
                    block.col = col;
                    block.x = this.gridStartX + col * this.blockSize;
                    block.y = this.gridStartY + row * this.blockSize;
                    this.grid[row][col] = block;
                    blockIndex++;
                } else {
                    this.grid[row][col] = null;
                }
            }
        }

        console.log('网格已打乱');
    }

    // 处理特殊方块交换
    processSpecialSwap(block1, block2, row1, col1, row2, col2) {
        const type1 = block1.blockType;
        const type2 = block2.blockType;
        let score = 0;
        let eliminatedBlocks = [];

        // 火箭和任意格子交换 - 先交换位置，再消除交换后火箭所在的列，使用配置中的分数
        if ((block1.special === 'rocket' && block2.special !== 'rocket') ||
            (block2.special === 'rocket' && block1.special !== 'rocket')) {

            // 先执行实际的方块交换
            this.swapBlocks(row1, col1, row2, col2);
            console.log(`火箭交换: (${row1},${col1}) <-> (${row2},${col2})`);

            // 火箭交换后的位置：如果block1是火箭，交换后火箭在col2位置；如果block2是火箭，交换后火箭在col1位置
            const rocketCol = block1.special === 'rocket' ? col2 : col1;
            const rocketRow = block1.special === 'rocket' ? row2 : row1;

            // 消除火箭所在的列，但要包括火箭本身
            eliminatedBlocks = this.eliminateColumn(rocketCol, []);

            // 使用配置中的分数
            score = this.uiConfig ? this.uiConfig.getSwapScore('normal', 'rocket') : 120;

            console.log(`火箭消除列 ${rocketCol}，消除了 ${eliminatedBlocks.length} 个方块，获得 ${score} 分`);

            // 播放火箭音效
            if (this.gameManager.audioManager) {
                this.gameManager.audioManager.playSound('shua');
            }
        }
        // 两个小火箭交换 - 十字消除，使用配置中的分数
        else if (block1.special === 'rocket' && block2.special === 'rocket') {
            eliminatedBlocks = this.eliminateCross(row1, col1);

            // 使用配置中的分数
            score = this.uiConfig ? this.uiConfig.getSwapScore('rocket', 'rocket') : 200;

            console.log(`火箭+火箭交换，十字消除 ${eliminatedBlocks.length} 个方块，获得 ${score} 分`);

            // 播放火箭音效
            if (this.gameManager.audioManager) {
                this.gameManager.audioManager.playSound('shua');
            }
        }
        // 普通格子和小炸弹交换 - 先交换位置，再以交换后的炸弹位置为中心3x3范围爆炸，使用配置中的分数
        else if ((type1 === 'normal' && block2.special === 'bomb') ||
                 (type2 === 'normal' && block1.special === 'bomb')) {

            // 先执行实际的方块交换
            this.swapBlocks(row1, col1, row2, col2);
            console.log(`炸弹交换: (${row1},${col1}) <-> (${row2},${col2})`);

            // 炸弹交换后的位置：如果block1是炸弹，交换后炸弹在(row2,col2)位置；如果block2是炸弹，交换后炸弹在(row1,col1)位置
            const bombRow = block1.special === 'bomb' ? row2 : row1;
            const bombCol = block1.special === 'bomb' ? col2 : col1;

            eliminatedBlocks = this.eliminateArea(bombRow, bombCol, 3);

            // 使用配置中的分数
            score = this.uiConfig ? this.uiConfig.getSwapScore('normal', 'bomb') : 200;

            console.log(`炸弹在交换后位置(${bombRow}, ${bombCol})爆炸，消除了 ${eliminatedBlocks.length} 个方块，获得 ${score} 分`);

            // 播放炸弹音效和爆炸特效
            if (this.gameManager.audioManager) {
                this.gameManager.audioManager.playSound('bomb');
            }
            this.addExplosionEffect(bombCol * this.blockSize + this.gridStartX + this.blockSize/2,
                                  bombRow * this.blockSize + this.gridStartY + this.blockSize/2);
        }
        // 小火箭与小炸弹交换 - 消除火箭所在列为中心的3列，使用配置中的分数
        else if ((block1.special === 'rocket' && block2.special === 'bomb') ||
                 (block1.special === 'bomb' && block2.special === 'rocket')) {

            // 先交换位置，然后以交换后的火箭位置为中心消除3列
            this.swapBlocks(row1, col1, row2, col2);

            // 交换后火箭的位置
            const rocketCol = block1.special === 'rocket' ? col2 : col1;
            eliminatedBlocks = this.eliminateThreeColumns(rocketCol);

            // 使用配置中的分数
            score = this.uiConfig ? this.uiConfig.getSwapScore('rocket', 'bomb') : 300;

            console.log(`火箭+炸弹交换，消除以列 ${rocketCol} 为中心的3列，消除了 ${eliminatedBlocks.length} 个方块，获得 ${score} 分`);

            // 播放炸弹音效
            if (this.gameManager.audioManager) {
                this.gameManager.audioManager.playSound('bomb');
            }
        }
        // 小炸弹和小炸弹交换 - 5x5范围爆炸，使用配置中的分数
        else if (block1.special === 'bomb' && block2.special === 'bomb') {
            const centerRow = Math.floor((row1 + row2) / 2);
            const centerCol = Math.floor((col1 + col2) / 2);
            eliminatedBlocks = this.eliminateArea(centerRow, centerCol, 5);

            // 使用配置中的分数
            score = this.uiConfig ? this.uiConfig.getSwapScore('bomb', 'bomb') : 500;

            console.log(`炸弹+炸弹交换，5x5爆炸，消除了 ${eliminatedBlocks.length} 个方块，获得 ${score} 分`);

            // 播放炸弹音效和大爆炸特效
            if (this.gameManager.audioManager) {
                this.gameManager.audioManager.playSound('bomb');
            }
            this.addExplosionEffect(centerCol * this.blockSize + this.gridStartX + this.blockSize/2,
                                  centerRow * this.blockSize + this.gridStartY + this.blockSize/2, true);
        }

        return { score, eliminatedBlocks };
    }

    // 交换两个方块的位置
    swapBlocks(row1, col1, row2, col2) {
        const temp = this.grid[row1][col1];
        this.grid[row1][col1] = this.grid[row2][col2];
        this.grid[row2][col2] = temp;

        // 更新方块的位置信息（如果有的话）
        if (this.grid[row1][col1]) {
            this.grid[row1][col1].row = row1;
            this.grid[row1][col1].col = col1;
        }
        if (this.grid[row2][col2]) {
            this.grid[row2][col2].row = row2;
            this.grid[row2][col2].col = col2;
        }
    }

    // 消除整列
    eliminateColumn(col, excludePositions = []) {
        const eliminatedBlocks = [];
        for (let row = 0; row < this.gridSizeY; row++) {
            const block = this.grid[row][col];
            if (block) {
                // 检查是否需要排除这个位置
                const shouldExclude = excludePositions.some(pos => pos.row === row && pos.col === col);
                if (!shouldExclude) {
                    eliminatedBlocks.push({ row, col, block });
                    this.addParticleEffect(block.x + this.blockSize / 2, block.y + this.blockSize / 2, block.color);
                }
            }
        }
        return eliminatedBlocks;
    }

    // 消除十字形区域
    eliminateCross(centerRow, centerCol) {
        const eliminatedBlocks = [];

        // 消除整行
        for (let col = 0; col < this.gridSizeX; col++) {
            const block = this.grid[centerRow][col];
            if (block) {
                eliminatedBlocks.push({ row: centerRow, col, block });
                this.addParticleEffect(block.x + this.blockSize / 2, block.y + this.blockSize / 2, block.color);
            }
        }

        // 消除整列
        for (let row = 0; row < this.gridSizeY; row++) {
            if (row !== centerRow) { // 避免重复消除中心点
                const block = this.grid[row][centerCol];
                if (block) {
                    eliminatedBlocks.push({ row, col: centerCol, block });
                    this.addParticleEffect(block.x + this.blockSize / 2, block.y + this.blockSize / 2, block.color);
                }
            }
        }

        return eliminatedBlocks;
    }

    // 消除矩形区域
    eliminateArea(centerRow, centerCol, size) {
        const eliminatedBlocks = [];
        const radius = Math.floor(size / 2);

        for (let row = Math.max(0, centerRow - radius); row <= Math.min(this.gridSizeY - 1, centerRow + radius); row++) {
            for (let col = Math.max(0, centerCol - radius); col <= Math.min(this.gridSizeX - 1, centerCol + radius); col++) {
                const block = this.grid[row][col];
                if (block) {
                    eliminatedBlocks.push({ row, col, block });
                    // 标记方块为已移除
                    block.isMatched = true;
                    // 添加爆炸特效
                    this.addParticleEffect(block.x + this.blockSize / 2, block.y + this.blockSize / 2, block.color);
                    console.log(`炸弹消除方块: (${row}, ${col})`);
                }
            }
        }

        return eliminatedBlocks;
    }

    // 消除三列
    eliminateThreeColumns(centerCol) {
        const eliminatedBlocks = [];
        const startCol = Math.max(0, centerCol - 1);
        const endCol = Math.min(this.gridSizeX - 1, centerCol + 1);

        for (let col = startCol; col <= endCol; col++) {
            for (let row = 0; row < this.gridSizeY; row++) {
                const block = this.grid[row][col];
                if (block) {
                    eliminatedBlocks.push({ row, col, block });
                    this.addParticleEffect(block.x + this.blockSize / 2, block.y + this.blockSize / 2, block.color);
                }
            }
        }

        return eliminatedBlocks;
    }

    // 添加爆炸效果
    addExplosionEffect(x, y, isBig = false) {
        const particleCount = isBig ? 20 : 12;
        const colors = ['#FF4500', '#FF6347', '#FFD700', '#FF69B4', '#00CED1'];

        for (let i = 0; i < particleCount; i++) {
            this.particles.push({
                x: x,
                y: y,
                vx: (Math.random() - 0.5) * (isBig ? 15 : 10),
                vy: (Math.random() - 0.5) * (isBig ? 15 : 10),
                size: Math.random() * (isBig ? 8 : 6) + (isBig ? 4 : 2),
                color: colors[Math.floor(Math.random() * colors.length)],
                life: 1,
                decay: 0.015
            });
        }

        // 添加爆炸闪烁效果
        for (let i = 0; i < (isBig ? 8 : 5); i++) {
            this.sparkles.push({
                x: x + (Math.random() - 0.5) * (isBig ? 40 : 25),
                y: y + (Math.random() - 0.5) * (isBig ? 40 : 25),
                size: Math.random() * (isBig ? 12 : 8) + (isBig ? 6 : 4),
                life: 1,
                decay: 0.02,
                twinkle: Math.random() * Math.PI * 2
            });
        }
    }
    
    // 添加粒子效果
    addParticleEffect(x, y, color) {
        for (let i = 0; i < 8; i++) {
            this.particles.push({
                x: x,
                y: y,
                vx: (Math.random() - 0.5) * 8,
                vy: (Math.random() - 0.5) * 8,
                size: Math.random() * 4 + 2,
                color: color,
                life: 1,
                decay: 0.02
            });
        }
        
        // 添加闪烁效果
        for (let i = 0; i < 3; i++) {
            this.sparkles.push({
                x: x + (Math.random() - 0.5) * 20,
                y: y + (Math.random() - 0.5) * 20,
                size: Math.random() * 6 + 4,
                life: 1,
                decay: 0.03,
                twinkle: 0
            });
        }
    }
    
    // 添加浮动文字
    addFloatingText(text, x, y, color) {
        this.floatingTexts.push({
            text: text,
            x: x,
            y: y,
            vx: 0,
            vy: -2,
            color: color,
            life: 1,
            decay: 0.015,
            scale: 1
        });
    }

    // 添加连击文字（更大更醒目）
    addComboText(comboCount, multiplier) {
        // 根据连击数确定大小和颜色
        let scale, color, duration;

        if (comboCount >= 6) {
            scale = 2.8;
            color = '#FF0000'; // 红色 - 6次以上
            duration = 1.2;
        } else if (comboCount >= 3) {
            scale = 2.3;
            color = '#FF8C00'; // 橘黄色 - 3-5次
            duration = 1.0;
        } else {
            scale = 2.0;
            color = '#32CD32'; // 绿色 - 3次以下
            duration = 0.8;
        }

        this.floatingTexts.push({
            text: `${comboCount}连击! x${multiplier}`,
            x: this.canvas.width / 2,
            y: this.canvas.height / 2 - 50,
            vx: 0,
            vy: -1,
            color: color,
            life: 1,
            decay: 0.015, // 缩短动画时间
            scale: scale,
            isCombo: true, // 标记为连击文字
            pulsePhase: 0, // 脉动相位
            duration: duration
        });

        console.log(`显示连击文字: ${comboCount}连击! x${multiplier}`);
    }
    
    // 重新开始游戏
    restartGame() {
        this.resetScore();
        this.isGameOver = false;
        this.isLevelComplete = false;
        this.showLevelCompleteUI = false;
        this.isAnimating = false;
        this.showExitDialog = false;
        this.isBombCardSelecting = false;
        
        // 清空特效
        this.particles = [];
        this.sparkles = [];
        this.floatingTexts = [];
        this.animations = [];
        
        // 重置道具数量为初始值
        this.props = {
            refresh: this.uiConfig ? this.uiConfig.getPropInitialCount('refresh') : 3,
            bomb: this.uiConfig ? this.uiConfig.getPropInitialCount('bomb') : 2,
            clear: this.uiConfig ? this.uiConfig.getPropInitialCount('clear') : 1
        };
        
        this.initGrid();
        console.log('游戏重新开始');
    }
    
    // 检查游戏状态
    checkGameStatus() {
        // 检查是否达到目标分数，但要等所有连消完毕
        if (this.score >= this.targetScore && !this.isAnimating) {
            if (!this.isLevelComplete) {
                this.isLevelComplete = true;
                console.log('恭喜过关！');

                // 播放胜利音效
                if (this.gameManager.audioManager) {
                    this.gameManager.audioManager.playSound('win');
                }

                // 添加庆祝效果
                this.addCelebrationEffect();

                // 延迟显示通关提示
                setTimeout(() => {
                    this.showLevelCompleteDialog();
                }, 1000);
            }
        }
    }

    // 显示通关对话框
    showLevelCompleteDialog() {
        // 这里可以显示通关对话框，提示用户进入下一关
        console.log('显示通关对话框');
        // 设置一个标志位来显示通关UI
        this.showLevelCompleteUI = true;
    }
    
    // 添加庆祝效果
    addCelebrationEffect() {
        // 添加大量彩色粒子
        for (let i = 0; i < 50; i++) {
            this.particles.push({
                x: Math.random() * this.canvas.width,
                y: Math.random() * this.canvas.height,
                vx: (Math.random() - 0.5) * 10,
                vy: (Math.random() - 0.5) * 10,
                size: Math.random() * 8 + 4,
                color: ['#FFD700', '#FF69B4', '#00CED1', '#98FB98', '#DDA0DD'][Math.floor(Math.random() * 5)],
                life: 1,
                decay: 0.01
            });
        }
        
        // 添加闪烁星星
        for (let i = 0; i < 20; i++) {
            this.sparkles.push({
                x: Math.random() * this.canvas.width,
                y: Math.random() * this.canvas.height,
                size: Math.random() * 10 + 5,
                life: 1,
                decay: 0.008,
                twinkle: Math.random() * Math.PI * 2
            });
        }
    }
    
    // 更新游戏状态
    update() {
        this.animationTime += 0.016; // 60fps
        
        // 更新背景效果
        try {
            if (this.backgroundStars && typeof BackgroundUtils !== 'undefined') {
                BackgroundUtils.updateStars(this.backgroundStars);
            }
        } catch (error) {
            console.warn('背景更新失败:', error);
        }
        
        // 更新动画控制器
        if (this.animator) {
            this.animator.update();
            this.isAnimating = this.animator.hasActiveAnimations();
        }
        
        // 更新粒子效果
        this.updateParticles();
        
        // 更新闪烁效果
        this.updateSparkles();
        
        // 更新浮动文字
        this.updateFloatingTexts();
        
        // 更新方块动画
        this.updateBlockAnimations();
        
        // 更新游戏状态
        if (this.animator) {
            this.animator.update();
            this.isAnimating = this.animator.hasActiveAnimations();
        }
    }
    
    // 更新粒子效果
    updateParticles() {
        this.particles = this.particles.filter(particle => {
            particle.x += particle.vx;
            particle.y += particle.vy;
            particle.life -= particle.decay;
            particle.vx *= 0.98;
            particle.vy *= 0.98;
            return particle.life > 0;
        });
    }
    
    // 更新闪烁效果
    updateSparkles() {
        this.sparkles = this.sparkles.filter(sparkle => {
            sparkle.life -= sparkle.decay;
            sparkle.twinkle += 0.2;
            return sparkle.life > 0;
        });
    }
    
    // 更新浮动文字
    updateFloatingTexts() {
        this.floatingTexts = this.floatingTexts.filter(text => {
            text.x += text.vx;
            text.y += text.vy;
            text.life -= text.decay;
            text.scale = Math.max(0.5, text.scale - 0.01);
            return text.life > 0;
        });
    }
    
    // 更新方块动画（简化版本，移除浮动效果）
    updateBlockAnimations() {
        if (!this.grid) return;
        
        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX; col++) {
                const block = this.grid[row][col];
                if (block) {
                    // 选中状态的特殊效果（保持静态）
                    if (block.isSelected) {
                        block.glowIntensity = Math.min(1, block.glowIntensity + 0.1);
                    } else {
                        block.glowIntensity = Math.max(0, block.glowIntensity - 0.05);
                    }
                    
                    // 匹配状态的消失动画
                    if (block.isMatched) {
                        block.alpha = Math.max(0, block.alpha - 0.1);
                        block.scale = Math.max(0, block.scale - 0.05);
                        block.rotation += 0.2;
                    } else {
                        // 保持默认状态
                        block.scale = 1;
                        block.rotation = 0;
                    }
                }
            }
        }
    }
    
    // 创建粒子效果
    createParticles(x, y, color, count = 8) {
        for (let i = 0; i < count; i++) {
            this.particles.push({
                x: x,
                y: y,
                vx: (Math.random() - 0.5) * 8,
                vy: (Math.random() - 0.5) * 8,
                size: Math.random() * 4 + 2,
                color: color,
                life: 1,
                decay: 0.02
            });
        }
    }
    
    // 创建闪烁效果
    createSparkles(x, y, count = 3) {
        for (let i = 0; i < count; i++) {
            this.sparkles.push({
                x: x + (Math.random() - 0.5) * 20,
                y: y + (Math.random() - 0.5) * 20,
                size: Math.random() * 6 + 4,
                life: 1,
                decay: 0.03,
                twinkle: 0
            });
        }
    }
    
    // 创建浮动文字
    createFloatingText(x, y, text, color) {
        this.floatingTexts.push({
            text: text,
            x: x,
            y: y,
            vx: 0,
            vy: -2,
            color: color,
            life: 1,
            decay: 0.015,
            scale: 1
        });
    }
    
    // 网格起始位置（供事件处理使用）
    get gridStartX() {
        if (this.uiConfig) {
            const gridConfig = this.uiConfig.getGridConfig();
            return gridConfig.startX;
        }
        // 后备方案：使用原来的计算方式
        return (this.canvas.width - this.gridSizeX * this.blockSize) / 2;
    }

    get gridStartY() {
        if (this.uiConfig) {
            const gridConfig = this.uiConfig.getGridConfig();
            return gridConfig.startY;
        }
        // 后备方案：使用静态配置
        return GamePageCore.LAYOUT.GRID_START_Y;
    }

    // 获取网格间距
    getGridSpacing() {
        if (this.uiConfig) {
            // 优先使用可爱风网格配置
            const cuteGridConfig = this.uiConfig.getGameGridConfig();
            if (cuteGridConfig && cuteGridConfig.cell && cuteGridConfig.cell.spacing) {
                return cuteGridConfig.cell.spacing;
            }

            // 后备方案：使用普通网格配置
            const gridConfig = this.uiConfig.getGridConfig();
            return gridConfig.padding || 0;
        }
        // 最终后备方案
        return 3;
    }
    
    // 静态方法：获取布局配置
    static getLayout() {
        return GamePageCore.LAYOUT;
    }
    
    // 静态方法：更新网格Y坐标
    static setGridStartY(y) {
        GamePageCore.LAYOUT.GRID_START_Y = y;
    }
    
    // 静态方法：获取网格起始X坐标（80%宽度居中）
    static getGridStartX(canvasWidth) {
        const gridWidth = GamePageCore.LAYOUT.GRID_SIZE_X * GamePageCore.LAYOUT.BLOCK_SIZE;
        const availableWidth = canvasWidth * 0.8; // 80%宽度
        const startX = (canvasWidth - availableWidth) / 2; // 居中
        return startX + (availableWidth - gridWidth) / 2; // 在80%区域内居中
    }
    
    // 静态方法：获取网格起始Y坐标
    static getGridStartY() {
        return GamePageCore.LAYOUT.GRID_START_Y;
    }
    
    // 初始化方法
    init() {
        console.log('GamePageCore初始化');
        this.initGrid();
        this.initBackgroundEffects();
        this.loadAnimalImages();
        this.initAnimator();
    }
    
    // 初始化动画控制器
    initAnimator() {
        try {
            if (typeof GamePageAnimator !== 'undefined') {
                this.animator = new GamePageAnimator(this);
                this.animator.init();
                console.log('动画控制器初始化成功');
            } else {
                console.warn('GamePageAnimator未加载，使用简化版本');
                this.animator = null;
            }
        } catch (error) {
            console.error('动画控制器初始化失败:', error);
            this.animator = null;
        }
    }

    // 创建默认UI配置（当GameUIConfig未加载时的后备方案）
    createDefaultUIConfig() {
        return {
            getBackButtonConfig: () => ({
                x: 20,
                y: 75, // 135 + 50 - 115 + 5 (默认计算)
                width: 100,
                height: 32,
                text: '返回'
            }),
            getStatsBarConfig: () => ({
                x: (this.canvas.width - this.canvas.width * 0.8) / 2,
                y: 135,
                width: this.canvas.width * 0.8,
                height: 120,
                borderRadius: 15,
                innerBorder: {
                    x: (this.canvas.width - this.canvas.width * 0.8) / 2 + 5,
                    y: 140,
                    width: this.canvas.width * 0.8 - 10,
                    height: 110,
                    borderRadius: 12
                }
            }),
            getGridConfig: () => {
                // 动态计算方块大小（默认配置）
                const availableWidth = this.canvas.width * 0.8;
                const cols = 6, rows = 8, padding = 2;
                const totalPadding = (cols - 1) * padding;
                const blockSize = Math.floor((availableWidth - totalPadding) / cols);
                const actualGridWidth = cols * blockSize + totalPadding;
                const startX = (this.canvas.width - actualGridWidth) / 2;

                return {
                    startX: startX,
                    startY: 300,
                    cols: cols,
                    rows: rows,
                    blockSize: blockSize,
                    padding: padding,
                    gridWidth: actualGridWidth,
                    gridHeight: rows * blockSize + (rows - 1) * padding
                };
            },
            getPropBarConfig: () => {
                const barWidth = this.canvas.width * 0.8;
                const barX = (this.canvas.width - barWidth) / 2;
                const propSpacing = (barWidth - 60) / 3;
                return {
                    x: barX,
                    y: 300 + 8 * 60 + 20,
                    width: barWidth,
                    height: 80,
                    propSize: 60,
                    propSpacing: propSpacing,
                    propStartX: barX + 30,
                    borderRadius: 12,
                    innerBorderRadius: 10
                };
            },
            updateCanvasSize: () => {},
            updateConfig: () => false,
            batchUpdateConfig: () => ({}),
            getConfigSummary: () => ({})
        };
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GamePageCore;
} else {
    window.GamePageCore = GamePageCore;
}