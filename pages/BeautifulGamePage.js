// 美化版游戏页面 - 整合所有游戏模块
class BeautifulGamePage {
    constructor(gameManager, levelConfig) {
        this.gameManager = gameManager;
        
        // 初始化核心模块
        this.core = new GamePageCore(gameManager, levelConfig);
        this.renderer = new GamePageRenderer(this.core);
        this.events = new GamePageEvents(this.core, this.renderer);

        // 设置相互引用
        this.core.events = this.events;

        console.log('BeautifulGamePage初始化完成');
    }
    
    // 初始化游戏页面
    init() {
        console.log('初始化美化版游戏页面');
        
        // 初始化各个模块
        this.core.init();
        this.events.init();
        
        console.log('美化版游戏页面初始化完成');
    }
    
    // 更新游戏状态
    update() {
        this.core.update();
    }
    
    // 渲染游戏画面
    render() {
        this.renderer.render();
    }
    
    // 清理资源
    destroy() {
        console.log('清理BeautifulGamePage资源');
        
        if (this.events) {
            this.events.destroy();
        }
        
        console.log('BeautifulGamePage资源清理完成');
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BeautifulGamePage;
} else {
    window.BeautifulGamePage = BeautifulGamePage;
}
