/**
 * 游戏页面动画控制器 - 协调掉落、生成、匹配等动画
 */
// 导入GamePageCore类
const GamePageCore = require('./GamePageCore');
class GamePageAnimator {
    constructor(core) {
        this.core = core;
        this.canvas = core.canvas;
        this.ctx = core.ctx;
        
        // 子系统
        this.falling = null;
        this.generator = null;
        this.matcher = null;
        
        // 动画状态
        this.isAnimating = false;
        this.animationQueue = [];
        this.currentAnimation = null;
        
        console.log('GamePageAnimator初始化完成');
    }
    
    // 初始化子系统
    init() {
        // 创建子系统实例
        this.falling = new GamePageFalling(this.core);
        this.generator = new GamePageGenerator(this.core);
        this.matcher = new GamePageMatcher(this.core);
        
        console.log('动画控制器子系统初始化完成');
    }
    
    // 处理方块交换后的完整流程
    async processSwapResult(swapResult) {
        if (!swapResult.success) {
            return false;
        }
        
        console.log('开始处理交换结果');
        this.isAnimating = true;
        
        try {
            // 首先检查初始交换是否产生匹配
            const initialMatchResult = this.matcher.findAllMatches();
            const hasInitialMatch = initialMatchResult.matches.length > 0;

            console.log(`初始匹配检查: ${hasInitialMatch ? '有匹配' : '无匹配'}, 匹配数: ${initialMatchResult.matches.length}`);

            // 如果初始交换没有产生匹配，直接返回false
            if (!hasInitialMatch) {
                console.log('初始交换无匹配，将触发复位');
                return false;
            }

            // 循环处理匹配和掉落，直到没有新的匹配
            let hasMoreMatches = true;
            let totalScore = 0;
            let roundCount = 0;

            while (hasMoreMatches) {
                roundCount++;
                console.log(`开始第${roundCount}轮匹配处理`);

                // 1. 检查匹配
                const matchResult = this.matcher.findAllMatches();

                if (matchResult.matches.length > 0) {
                    // 2. 处理匹配消除并获取被移除的位置
                    const matchResult2 = await this.matcher.processMatches(matchResult);
                    console.log(`第${roundCount}轮匹配处理结果:`, matchResult2);
                    console.log(`本次得分: ${matchResult2.score}, 累计得分: ${totalScore}`);
                    totalScore += matchResult2.score;

                    // 3. 获取被移除的位置
                    const removedPositions = matchResult2.removedPositions;

                    // 4. 处理方块掉落和生成新方块（普通消除）
                    await this.falling.processFalling(removedPositions, 'normal');

                    // 5. 等待所有动画完成
                    await this.waitForAnimationsComplete();

                } else {
                    hasMoreMatches = false;
                    console.log(`第${roundCount}轮无新匹配，结束处理`);
                }
            }


            // 只有当有得分时才更新分数
            if (totalScore > 0) {
                this.core.updateScore(totalScore);
                console.log(`更新总分数: ${totalScore}`);
            }

            // 检查游戏状态
            this.core.checkGameStatus();

            console.log(`交换处理完成，总轮数: ${roundCount}, 总得分: ${totalScore}`);

            // 由于已经确认初始交换有匹配，总是返回true，避免复位
            return true;
            
        } catch (error) {
            console.error('处理交换结果时出错:', error);
            return false;
        } finally {
            this.isAnimating = false;
        }
    }
    
    // 生成新方块
    async generateNewBlocks(removedPositions) {
        // 统计每列需要生成的数量
        const columnCounts = {};
        removedPositions.forEach(pos => {
            columnCounts[pos.col] = (columnCounts[pos.col] || 0) + 1;
        });
        
        // 为每列请求生成
        const promises = [];
        Object.keys(columnCounts).forEach(col => {
            const count = columnCounts[col];
            promises.push(this.generateBlocksForColumn(parseInt(col), count));
        });
        
        // 等待所有生成完成
        await Promise.all(promises);
    }
    
    // 为指定列生成方块
    generateBlocksForColumn(col, count) {
        return new Promise((resolve) => {
            let generated = 0;
            
            const generateNext = () => {
                if (generated >= count) {
                    resolve();
                    return;
                }
                
                // 找到目标位置
                const targetRow = this.findTopEmptyRow(col);
                if (targetRow >= 0) {
                    // 创建新方块
                    const newBlock = this.core.createRandomBlock(
                        targetRow,
                        col,
                        this.core.gridStartX,
                        this.core.gridStartY
                    );
                    
                    if (newBlock) {
                        // 设置从顶部掉落
                        newBlock.y = this.core.gridStartY - (generated + 1) * this.core.blockSize;
                        newBlock.targetY = this.core.gridStartY + targetRow * this.core.blockSize;
                        newBlock.isFalling = true;
                        newBlock.fallVelocity = 2; // 设置初始速度
                        
                        // 添加到掉落系统
                        this.falling.fallingBlocks.push({
                            block: newBlock,
                            fromRow: -1 - generated,
                            toRow: targetRow,
                            col: col,
                            startTime: Date.now() + generated * 100,
                            isNew: true
                        });
                        
                        generated++;
                        
                        // 延迟生成下一个
                        setTimeout(generateNext, 100);
                    } else {
                        resolve();
                    }
                } else {
                    resolve();
                }
            };
            
            generateNext();
        });
    }
    
    // 找到列中最顶部的空位
    findTopEmptyRow(col) {
        for (let row = 0; row < this.core.gridSizeY; row++) {
            if (!this.core.grid[row] || !this.core.grid[row][col] || this.core.grid[row][col].isRemoved) {
                return row;
            }
        }
        return -1;
    }
    
    // 等待所有动画完成
    waitForAnimationsComplete() {
        return new Promise((resolve) => {
            const checkComplete = () => {
                const fallingComplete = !this.falling.isFalling();
                const generatingComplete = !this.generator.isGeneratingBlocks();
                const matchingComplete = !this.matcher.isProcessing();
                
                if (fallingComplete && generatingComplete && matchingComplete) {
                    resolve();
                } else {
                    setTimeout(checkComplete, 50);
                }
            };
            
            checkComplete();
        });
    }
    
    // 更新所有动画
    update() {
        if (this.falling) {
            this.falling.update();
        }
        
        // 更新其他动画系统...
    }
    
    // 渲染所有动画效果
    render(renderer) {
        if (this.falling) {
            this.falling.renderFallingBlocks(renderer);
        }
        
        // 渲染其他动画效果...
    }
    
    // 检查是否有动画正在进行
    hasActiveAnimations() {
        return this.isAnimating || 
               (this.falling && this.falling.isFalling()) ||
               (this.generator && this.generator.isGeneratingBlocks()) ||
               (this.matcher && this.matcher.isProcessing());
    }
    
    // 暂停所有动画
    pauseAnimations() {
        if (this.generator) {
            this.generator.pause();
        }
        
        console.log('所有动画已暂停');
    }
    
    // 恢复所有动画
    resumeAnimations() {
        if (this.generator) {
            this.generator.resume();
        }
        
        console.log('所有动画已恢复');
    }
    
    // 清理所有动画
    clear() {
        this.isAnimating = false;
        this.animationQueue = [];
        this.currentAnimation = null;
        
        if (this.falling) {
            this.falling.clear();
        }
        
        if (this.generator) {
            this.generator.clear();
        }
        
        if (this.matcher) {
            this.matcher.clear();
        }
        
        console.log('动画控制器已清理');
    }
    
    // 获取动画状态
    getAnimationStatus() {
        return {
            isAnimating: this.isAnimating,
            falling: this.falling ? this.falling.fallingBlocks.length : 0,
            generating: this.generator ? this.generator.getGenerationStats() : null,
            matching: this.matcher ? this.matcher.getMatchStats() : null
        };
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GamePageAnimator;
} else {
    window.GamePageAnimator = GamePageAnimator;
}