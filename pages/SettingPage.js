/**
 * 设置页面模块
 * 负责音量控制和游戏设置功能
 */

// 导入背景工具类
let BackgroundUtils, BackgroundRenderer;
try {
    if (typeof require !== 'undefined') {
        const bgUtils = require('../utils/BackgroundUtils.js');
        BackgroundUtils = bgUtils.BackgroundUtils;
        BackgroundRenderer = bgUtils.BackgroundRenderer;
    } else {
        BackgroundUtils = window.BackgroundUtils;
        BackgroundRenderer = window.BackgroundRenderer;
    }
} catch (error) {
    console.warn('BackgroundUtils加载失败，使用简化版本');
}

class SettingPage {
    constructor(gameManager) {
        this.gameManager = gameManager;
        this.canvas = gameManager.canvas;
        this.ctx = gameManager.ctx;
        this.buttons = [];
        this.sliders = [];
        this.animationTime = 0;
        this.backgroundStars = null;
        this.init();
    }

    init() {
        console.log('初始化设置页面');
        this.initBackground();
        this.initButtons();
        this.initSliders();
        this.bindEvents();
    }

    initBackground() {
        if (BackgroundUtils) {
            this.backgroundStars = BackgroundUtils.createStars(15, this.canvas.width, this.canvas.height);
        }
    }

    initButtons() {
        this.buttons = [
            {
                id: 'back',
                text: '返回',
                x: 20,
                y: 70,
                width: 100,
                height: 32,
                action: () => this.goBack()
            }
        ];
    }

    initSliders() {
        const centerX = this.canvas.width / 2;
        
        // 静音模式开关
        this.muteButton = {
            x: centerX - 100,
            y: 200,
            width: 200,
            height: 50,
            text: this.gameManager.gameData.settings.muteMode ? '静音模式：开' : '静音模式：关'
        };

        this.sliders = [
            {
                id: 'bgmVolume',
                label: '背景音乐',
                x: centerX - 150,
                y: 300,
                width: 300,
                height: 20,
                value: this.gameManager.gameData.settings.bgmVolume,
                onChange: (value) => this.updateBgmVolume(value)
            },
            {
                id: 'effectVolume',
                label: '音效音量',
                x: centerX - 150,
                y: 380,
                width: 300,
                height: 20,
                value: this.gameManager.gameData.settings.effectVolume,
                onChange: (value) => this.updateEffectVolume(value)
            }
        ];
    }

    updateBgmVolume(value) {
        this.gameManager.gameData.settings.bgmVolume = value;
        this.gameManager.saveGameData();
        console.log(`背景音乐音量调整为: ${Math.round(value * 100)}%`);
    }

    updateEffectVolume(value) {
        this.gameManager.gameData.settings.effectVolume = value;
        this.gameManager.saveGameData();
        console.log(`音效音量调整为: ${Math.round(value * 100)}%`);
    }

    toggleMuteMode() {
        this.gameManager.gameData.settings.muteMode = !this.gameManager.gameData.settings.muteMode;
        this.muteButton.text = this.gameManager.gameData.settings.muteMode ? '静音模式：开' : '静音模式：关';
        this.gameManager.saveGameData();
        console.log(`静音模式: ${this.gameManager.gameData.settings.muteMode ? '开启' : '关闭'}`);
    }

    goBack() {
        console.log('返回主页面');
        this.gameManager.switchToPage('main');
    }

    bindEvents() {
        this.touchHandler = (res) => {
            if (res.touches && res.touches.length > 0) {
                const touch = res.touches[0];
                this.handleTouch(touch.clientX, touch.clientY);
            }
        };
        tt.onTouchStart(this.touchHandler);
    }

    handleTouch(x, y) {
        // 处理静音按钮点击
        if (this.isPointInButton(x, y, this.muteButton)) {
            this.toggleMuteMode();
            return;
        }
        
        // 处理按钮点击
        for (let button of this.buttons) {
            if (this.isPointInButton(x, y, button)) {
                console.log(`点击了按钮: ${button.text}`);
                button.action();
                return;
            }
        }
        
        // 处理滑块拖拽
        for (let slider of this.sliders) {
            if (this.isPointInSlider(x, y, slider)) {
                const newValue = Math.max(0, Math.min(1, (x - slider.x) / slider.width));
                slider.value = newValue;
                slider.onChange(newValue);
                return;
            }
        }
    }

    isPointInButton(x, y, button) {
        return x >= button.x && x <= button.x + button.width && y >= button.y && y <= button.y + button.height;
    }

    isPointInSlider(x, y, slider) {
        return x >= slider.x && x <= slider.x + slider.width && y >= slider.y - 10 && y <= slider.y + slider.height + 10;
    }

    update() {
        this.animationTime = (this.animationTime || 0) + 0.02;
        
        // 更新背景星星
        if (BackgroundUtils && this.backgroundStars) {
            BackgroundUtils.updateStars(this.backgroundStars);
        }
    }

    render() {
        // 使用背景工具类绘制统一背景
        if (BackgroundUtils && this.backgroundStars) {
            BackgroundUtils.updateStars(this.backgroundStars);
            BackgroundUtils.drawCompleteBackground(
                this.ctx,
                this.canvas.width,
                this.canvas.height,
                this.backgroundStars,
                this.animationTime || 0
            );
        } else {
            // 简化背景
            const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
            gradient.addColorStop(0, '#E1BEE7');
            gradient.addColorStop(1, '#F8BBD9');
            this.ctx.fillStyle = gradient;
            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        }
        
        const centerX = this.canvas.width / 2;
        
        // 绘制设置标题
        this.ctx.fillStyle = '#E65100';
        this.ctx.font = 'bold 32px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('设置', centerX, 150);
        
        // 绘制静音按钮
        this.drawMuteButton();
        
        // 绘制滑块
        this.drawSliders();
        
        // 绘制返回按钮 - 直接使用半透明风格
        const backButton = this.buttons.find(btn => btn.id === 'back');
        if (backButton) {
            this.renderBackButton(backButton.x, backButton.y, backButton.width, backButton.height, backButton.text);
        }
        
        // 绘制其他按钮（如果有的话）
        for (let button of this.buttons) {
            if (button.id !== 'back') {
                this.drawButton(button);
            }
        }
    }

    drawMuteButton() {
        const button = this.muteButton;
        
        // 绘制按钮背景
        this.ctx.fillStyle = this.gameManager.gameData.settings.muteMode ? '#4CAF50' : '#757575';
        this.ctx.beginPath();
        this.ctx.roundRect(button.x, button.y, button.width, button.height, 10);
        this.ctx.fill();
        
        // 绘制按钮边框
        this.ctx.strokeStyle = '#FFFFFF';
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.roundRect(button.x, button.y, button.width, button.height, 10);
        this.ctx.stroke();
        
        // 绘制按钮文字
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = 'bold 18px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(button.text, button.x + button.width / 2, button.y + button.height / 2 + 6);
    }

    drawSliders() {
        for (let slider of this.sliders) {
            // 绘制标签
            this.ctx.fillStyle = '#333333';
            this.ctx.font = 'bold 18px Arial';
            this.ctx.textAlign = 'left';
            this.ctx.fillText(slider.label, slider.x, slider.y - 10);
            
            // 绘制滑块轨道
            this.ctx.fillStyle = '#E0E0E0';
            this.ctx.fillRect(slider.x, slider.y, slider.width, slider.height);
            
            // 绘制滑块进度
            this.ctx.fillStyle = '#FF9800';
            this.ctx.fillRect(slider.x, slider.y, slider.width * slider.value, slider.height);
            
            // 绘制滑块手柄
            const handleX = slider.x + slider.width * slider.value - 10;
            this.ctx.fillStyle = '#FFFFFF';
            this.ctx.fillRect(handleX, slider.y - 5, 20, slider.height + 10);
            this.ctx.strokeStyle = '#FF9800';
            this.ctx.lineWidth = 2;
            this.ctx.strokeRect(handleX, slider.y - 5, 20, slider.height + 10);
            
            // 绘制数值
            this.ctx.fillStyle = '#666666';
            this.ctx.font = '16px Arial';
            this.ctx.textAlign = 'right';
            this.ctx.fillText(`${Math.round(slider.value * 100)}%`, slider.x + slider.width + 50, slider.y + 15);
        }
    }

    drawButton(button) {
        // 如果是返回按钮，使用与游戏界面一致的半透明样式
        if (button.id === 'back') {
            this.renderBackButton(button.x, button.y, button.width, button.height, button.text);
            return;
        }
        
        // 其他按钮使用原来的样式
        // 创建渐变背景
        const gradient = this.ctx.createLinearGradient(
            button.x, button.y, 
            button.x, button.y + button.height
        );
        
        gradient.addColorStop(0, button.color);
        gradient.addColorStop(1, this.adjustColor(button.color, -20));
        
        // 绘制圆角背景
        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.ctx.roundRect(button.x, button.y, button.width, button.height, 15);
        this.ctx.fill();
        
        // 添加光泽效果
        const glossGradient = this.ctx.createLinearGradient(
            button.x, button.y, 
            button.x, button.y + button.height / 2
        );
        glossGradient.addColorStop(0, 'rgba(255, 255, 255, 0.3)');
        glossGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
        this.ctx.fillStyle = glossGradient;
        this.ctx.beginPath();
        this.ctx.roundRect(button.x, button.y, button.width, button.height / 2, 15);
        this.ctx.fill();
        
        // 绘制边框
        this.ctx.strokeStyle = '#E0E0E0';
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.roundRect(button.x, button.y, button.width, button.height, 15);
        this.ctx.stroke();
        
        // 绘制按钮文字
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = 'bold 18px Arial';
        this.ctx.textAlign = 'center';
        
        // 添加文字阴影效果
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
        this.ctx.shadowBlur = 3;
        this.ctx.shadowOffsetX = 1;
        this.ctx.shadowOffsetY = 1;
        this.ctx.fillText(button.text, button.x + button.width / 2, button.y + button.height / 2 + 6);
        this.ctx.shadowColor = 'transparent';
    }
    
    // 渲染返回按钮（半透明白色背景，黑色字体）- 与GamePageRenderer保持一致
    renderBackButton(x, y, width, height, text) {
        this.ctx.save();
        
        // 按钮阴影
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
        this.ctx.shadowBlur = 6;
        this.ctx.shadowOffsetX = 2;
        this.ctx.shadowOffsetY = 2;
        
        // 半透明白色背景（透明度0.6）
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
        this.ctx.beginPath();
        this.ctx.roundRect(x, y, width, height, 15);
        this.ctx.fill();
        
        // 按钮边框
        this.ctx.shadowColor = 'transparent';
        this.ctx.strokeStyle = 'rgba(0, 0, 0, 0.3)';
        this.ctx.lineWidth = 1;
        this.ctx.stroke();
        
        // 黑色文字（确保完全居中）
        this.ctx.fillStyle = '#333333';
        this.ctx.font = 'bold 16px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        
        // 精确计算文本位置以确保完全居中
        const textX = Math.round(x + width / 2);
        const textY = Math.round(y + height / 2);
        this.ctx.fillText(text, textX, textY);
        
        this.ctx.restore();
    }
    
    // 辅助函数：调整颜色亮度
    adjustColor(color, amount) {
        // 如果是十六进制颜色
        if (color.startsWith('#')) {
            let r = parseInt(color.substr(1, 2), 16);
            let g = parseInt(color.substr(3, 2), 16);
            let b = parseInt(color.substr(5, 2), 16);
            
            r = Math.max(0, Math.min(255, r + amount));
            g = Math.max(0, Math.min(255, g + amount));
            b = Math.max(0, Math.min(255, b + amount));
            
            return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
        }
        return color;
    }

    destroy() {
        console.log('销毁设置页面');
        if (this.touchHandler) {
            tt.offTouchStart(this.touchHandler);
        }
        this.buttons = [];
        this.sliders = [];
        this.backgroundStars = null;
    }
}

// 导出模块 - 抖音小游戏环境兼容
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SettingPage;
} else {
    // 全局注册
    window.SettingPage = SettingPage;
}