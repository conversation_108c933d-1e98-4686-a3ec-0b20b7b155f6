/**
 * 简化的排行榜页面类
 */
class SimpleRankPage {
    constructor(gameManager) {
        this.gameManager = gameManager;
        this.canvas = gameManager.canvas;
        this.ctx = gameManager.ctx;
        
        // 排行榜数据
        this.rankData = [
            { name: '萌宠大师', score: 15000, level: 10 },
            { name: '消除达人', score: 12000, level: 8 },
            { name: '新手玩家', score: 8000, level: 5 },
            { name: '休闲玩家', score: 5000, level: 3 },
            { name: '初学者', score: 2000, level: 1 }
        ];
        
        // 返回按钮
        this.backButton = {
            x: 50,
            y: 50,
            width: 100,
            height: 40,
            text: '返回',
            color: '#FF6B6B'
        };
        
        console.log('SimpleRankPage初始化完成');
    }
    
    init() {
        console.log('初始化排行榜页面');
        this.setupTouchEvents();
        console.log('排行榜页面初始化完成');
    }
    
    handleTouchStart(x, y) {
        // 检查返回按钮点击
        if (this.isPointInButton(x, y, this.backButton)) {
            console.log('点击返回按钮');
            this.gameManager.switchToPage('main');
        }
    }
    
    isPointInButton(x, y, button) {
        return x >= button.x && x <= button.x + button.width &&
               y >= button.y && y <= button.y + button.height;
    }
    
    update() {
        // 排行榜页面暂时不需要动画更新
    }
    
    render() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        this.renderBackground();
        this.renderTitle();
        this.renderRankList();
        this.renderBackButton();
    }
    
    renderBackground() {
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        gradient.addColorStop(0, '#667eea');
        gradient.addColorStop(1, '#764ba2');
        
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }
    
    renderTitle() {
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = 'bold 36px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText('排行榜', this.canvas.width / 2, 120);
    }
    
    renderRankList() {
        const startY = 200;
        const itemHeight = 80;
        
        this.rankData.forEach((item, index) => {
            const y = startY + index * itemHeight;
            
            // 绘制排名背景
            this.ctx.fillStyle = index < 3 ? 'rgba(255, 215, 0, 0.3)' : 'rgba(255, 255, 255, 0.1)';
            this.ctx.fillRect(50, y - 30, this.canvas.width - 100, 60);
            
            // 绘制排名
            this.ctx.fillStyle = '#FFD700';
            this.ctx.font = 'bold 24px Arial, "Microsoft YaHei"';
            this.ctx.textAlign = 'left';
            this.ctx.fillText(`${index + 1}`, 80, y);
            
            // 绘制玩家名
            this.ctx.fillStyle = '#FFFFFF';
            this.ctx.font = '20px Arial, "Microsoft YaHei"';
            this.ctx.fillText(item.name, 150, y);
            
            // 绘制分数
            this.ctx.textAlign = 'right';
            this.ctx.fillText(`${item.score}分`, this.canvas.width - 80, y);
        });
    }
    
    renderBackButton() {
        // 绘制返回按钮
        this.ctx.fillStyle = this.backButton.color;
        this.ctx.fillRect(this.backButton.x, this.backButton.y, this.backButton.width, this.backButton.height);
        
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = 'bold 16px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText(
            this.backButton.text,
            this.backButton.x + this.backButton.width / 2,
            this.backButton.y + this.backButton.height / 2
        );
    }
    
    setupTouchEvents() {
        if (typeof tt !== 'undefined') {
            this.touchStartHandler = (e) => {
                if (e.touches && e.touches.length > 0) {
                    const touch = e.touches[0];
                    this.handleTouchStart(touch.clientX, touch.clientY);
                }
            };
            
            tt.onTouchStart(this.touchStartHandler);
        }
    }
    
    destroy() {
        console.log('清理SimpleRankPage资源');
        if (typeof tt !== 'undefined' && this.touchStartHandler) {
            tt.offTouchStart(this.touchStartHandler);
        }
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SimpleRankPage;
} else {
    window.SimpleRankPage = SimpleRankPage;
}