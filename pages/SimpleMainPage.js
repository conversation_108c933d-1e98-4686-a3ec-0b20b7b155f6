/**
 * 简化的主页面类 - 内联定义避免模块加载问题
 */
class SimpleMainPage {
    constructor(gameManager) {
        this.gameManager = gameManager;
        this.canvas = gameManager.canvas;
        this.ctx = gameManager.ctx;

        // 按钮配置
        this.buttons = [
            {
                id: 'start',
                text: '开始游戏',
                x: 0,
                y: 0,
                width: 200,
                height: 60,
                color: '#4CAF50',
                hoverColor: '#45a049'
            },
            {
                id: 'rank',
                text: '排行榜',
                x: 0,
                y: 0,
                width: 200,
                height: 60,
                color: '#2196F3',
                hoverColor: '#1976D2'
            },
            {
                id: 'setting',
                text: '设置',
                x: 0,
                y: 0,
                width: 200,
                height: 60,
                color: '#FF9800',
                hoverColor: '#F57C00'
            }
        ];

        // 动画状态
        this.titleAnimation = 0;
        this.animationTime = 0;
        this.titleFloatOffset = 0;
        this.titleRotation = 0;
        this.titleScale = 1;
        this.titleGlow = 0;

        // 背景效果
        this.backgroundScene = null;

        // 标题图片
        this.titleImage = null;
        this.titleImageLoaded = false;

        // UI配置
        this.uiConfig = null;

        console.log('SimpleMainPage初始化完成');
    }
    
    init() {
        console.log('初始化主页面');
        this.initUIConfig();
        this.calculateButtonPositions();
        this.initBackgroundEffects();
        this.loadTitleImage();
        this.setupTouchEvents();
        console.log('主页面初始化完成');
    }

    // 初始化UI配置
    initUIConfig() {
        try {
            if (typeof GameUIConfig !== 'undefined') {
                this.uiConfig = new GameUIConfig();
                console.log('主页UI配置初始化成功');
            } else {
                console.warn('GameUIConfig未加载，使用默认配置');
                this.uiConfig = null;
            }
        } catch (error) {
            console.error('初始化UI配置时出错:', error);
            this.uiConfig = null;
        }
    }

    // 加载标题图片
    loadTitleImage() {
        try {
            this.titleImage = new Image();

            // 使用配置中的图片路径
            if (this.uiConfig && this.uiConfig.images && this.uiConfig.images.title) {
                this.titleImage.src = this.uiConfig.images.title;
                console.log('使用配置中的标题图片路径:', this.uiConfig.images.title);
            } else {
                // 后备方案
                this.titleImage.src = 'images/title.png';
                console.log('使用默认标题图片路径: images/title.png');
            }

            this.titleImage.onload = () => {
                this.titleImageLoaded = true;
                console.log('标题图片加载成功');
            };

            this.titleImage.onerror = () => {
                console.warn('标题图片加载失败，将使用文字标题');
                this.titleImageLoaded = false;
            };
        } catch (error) {
            console.error('加载标题图片时出错:', error);
            this.titleImageLoaded = false;
        }
    }

    // 初始化背景效果
    initBackgroundEffects() {
        try {
            if (typeof BackgroundUtils !== 'undefined') {
                this.backgroundScene = BackgroundUtils.createBackgroundScene(
                    this.canvas.width,
                    this.canvas.height
                );
                console.log('背景效果初始化成功');
            } else {
                console.warn('BackgroundUtils未加载，使用简化背景');
                this.backgroundScene = null;
            }
        } catch (error) {
            console.warn('背景效果初始化失败:', error);
            this.backgroundScene = null;
        }
    }
    
    calculateButtonPositions() {
        const centerX = this.canvas.width / 2;
        const startY = this.canvas.height / 2 + 50;
        const spacing = 80;
        
        this.buttons.forEach((button, index) => {
            button.x = centerX - button.width / 2;
            button.y = startY + index * spacing;
        });
    }
    
    handleTouchStart(x, y) {
        this.buttons.forEach(button => {
            if (this.isPointInButton(x, y, button)) {
                this.handleButtonClick(button.id);
            }
        });
    }
    
    isPointInButton(x, y, button) {
        return x >= button.x && x <= button.x + button.width &&
               y >= button.y && y <= button.y + button.height;
    }
    
    handleButtonClick(buttonId) {
        console.log(`按钮点击: ${buttonId}`);
        
        switch (buttonId) {
            case 'start':
                const levelConfig = {
                    targetScore: 1000,
                    name: '萌宠新手村',
                    level: 1
                };
                this.gameManager.switchToPage('game', levelConfig);
                break;
            case 'rank':
                this.gameManager.switchToPage('rank');
                break;
            case 'setting':
                this.gameManager.switchToPage('setting');
                break;
            default:
                console.warn(`未知按钮: ${buttonId}`);
        }
    }

    // 更新标题动画状态
    updateTitleAnimation() {
        // 获取动画配置
        const titleConfig = this.uiConfig ? this.uiConfig.getTitleImageConfig() : null;

        if (titleConfig) {
            // 使用配置中的动画参数
            this.titleFloatOffset = Math.sin(this.animationTime * titleConfig.floatSpeed) * titleConfig.floatAmplitude;
            this.titleRotation = Math.sin(this.animationTime * titleConfig.rotationSpeed) * titleConfig.rotationAmplitude;
            this.titleScale = 1 + Math.sin(this.animationTime * titleConfig.scaleSpeed) * titleConfig.scaleAmplitude;
            this.titleGlow = titleConfig.glowMin + Math.sin(this.animationTime * titleConfig.glowSpeed) * (titleConfig.glowMax - titleConfig.glowMin);
        } else {
            // 后备方案：使用默认动画参数
            this.titleFloatOffset = Math.sin(this.animationTime * 0.8) * 8;
            this.titleRotation = Math.sin(this.animationTime * 0.6) * 0.02;
            this.titleScale = 1 + Math.sin(this.animationTime * 1.2) * 0.03;
            this.titleGlow = 0.3 + Math.sin(this.animationTime * 2) * 0.2;
        }

        // 保持原有的titleAnimation用于兼容
        this.titleAnimation = this.animationTime * 0.02;
    }

    update() {
        this.animationTime += 0.016; // 60fps

        // 更新标题动画状态
        this.updateTitleAnimation();

        // 更新背景效果
        if (this.backgroundScene && typeof BackgroundUtils !== 'undefined') {
            try {
                BackgroundUtils.updateBackgroundScene(this.backgroundScene, this.canvas.width, this.canvas.height);
            } catch (error) {
                console.warn('背景更新失败:', error);
            }
        }
    }
    
    render() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        this.renderBackground();
        this.renderTitle();
        this.renderButtons();
        this.renderVersionInfo();
    }
    
    renderBackground() {
        // 使用BackgroundUtils渲染完整背景场景
        if (this.backgroundScene && typeof BackgroundUtils !== 'undefined') {
            try {
                // 渲染暖系可爱风背景
                this.renderWarmCuteBackground();

                // 渲染彩虹
                BackgroundUtils.renderRainbow(this.ctx, this.backgroundScene.rainbow);

                // 渲染星星
                BackgroundUtils.renderStars(this.ctx, this.backgroundScene.stars);

                // 渲染浮动粒子
                BackgroundUtils.renderFloatingParticles(this.ctx, this.backgroundScene.particles);

            } catch (error) {
                console.warn('背景渲染失败:', error);
                this.renderSimpleWarmBackground();
            }
        } else {
            this.renderSimpleWarmBackground();
        }
    }

    // 渲染暖系可爱风背景
    renderWarmCuteBackground() {
        // 暖系渐变背景
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        gradient.addColorStop(0, '#FFE5B4');  // 桃色
        gradient.addColorStop(0.3, '#FFCCCB'); // 浅粉色
        gradient.addColorStop(0.6, '#FFB6C1'); // 淡粉色
        gradient.addColorStop(1, '#FFA07A');   // 浅橙色

        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // 添加可爱的装饰圆点
        this.ctx.save();
        for (let i = 0; i < 15; i++) {
            const x = (this.canvas.width / 15) * i + Math.sin(this.animationTime + i) * 20;
            const y = this.canvas.height * 0.2 + Math.cos(this.animationTime + i * 0.5) * 30;
            const radius = 3 + Math.sin(this.animationTime * 2 + i) * 2;
            const alpha = 0.1 + Math.sin(this.animationTime + i) * 0.05;

            this.ctx.globalAlpha = alpha;
            this.ctx.fillStyle = '#FFFFFF';
            this.ctx.beginPath();
            this.ctx.arc(x, y, radius, 0, Math.PI * 2);
            this.ctx.fill();
        }
        this.ctx.restore();
    }

    // 简化的暖系背景
    renderSimpleWarmBackground() {
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        gradient.addColorStop(0, '#FFE5B4');
        gradient.addColorStop(0.5, '#FFCCCB');
        gradient.addColorStop(1, '#FFA07A');

        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }
    
    renderTitle() {
        // 使用配置计算位置
        let titlePos;
        if (this.uiConfig) {
            titlePos = this.uiConfig.calculateTitleImagePosition(this.canvas.width, this.canvas.height);
        } else {
            // 后备方案
            titlePos = {
                x: this.canvas.width / 2,
                y: this.canvas.height / 3
            };
        }

        this.ctx.save();

        // 应用浮动和旋转变换
        this.ctx.translate(titlePos.x, titlePos.y + this.titleFloatOffset);
        this.ctx.rotate(this.titleRotation);
        this.ctx.scale(this.titleScale, this.titleScale);

        if (this.titleImageLoaded && this.titleImage) {
            // 渲染标题图片
            this.renderTitleImage();
        } else {
            // 后备方案：渲染文字标题
            this.renderTextTitle();
        }

        this.ctx.restore();

        // 渲染副标题（不受主标题动画影响）
        this.renderSubtitle();
    }

    // 渲染标题图片
    renderTitleImage() {
        // 添加发光效果
        this.ctx.shadowColor = `rgba(255, 215, 0, ${this.titleGlow})`;
        this.ctx.shadowBlur = 20;
        this.ctx.shadowOffsetX = 0;
        this.ctx.shadowOffsetY = 0;

        // 使用配置计算图片尺寸
        let imageSize;
        if (this.uiConfig) {
            imageSize = this.uiConfig.calculateTitleImageSize(
                this.canvas.width,
                this.titleImage.width,
                this.titleImage.height
            );
        } else {
            // 后备方案：使用默认计算
            const maxWidth = this.canvas.width * 0.6;
            const maxHeight = 120;

            let imageWidth = this.titleImage.width;
            let imageHeight = this.titleImage.height;

            if (imageWidth > maxWidth) {
                const ratio = maxWidth / imageWidth;
                imageWidth = maxWidth;
                imageHeight = imageHeight * ratio;
            }

            if (imageHeight > maxHeight) {
                const ratio = maxHeight / imageHeight;
                imageHeight = maxHeight;
                imageWidth = imageWidth * ratio;
            }

            imageSize = { width: imageWidth, height: imageHeight };
        }

        // 绘制图片（以原点为中心）
        this.ctx.drawImage(
            this.titleImage,
            -imageSize.width / 2,
            -imageSize.height / 2,
            imageSize.width,
            imageSize.height
        );
    }

    // 渲染文字标题（后备方案）
    renderTextTitle() {
        // 获取文字标题配置
        const textConfig = this.uiConfig ? this.uiConfig.getTextTitleConfig() : null;

        if (textConfig) {
            // 使用配置中的样式
            // 添加阴影效果
            this.ctx.shadowColor = textConfig.shadowColor;
            this.ctx.shadowBlur = textConfig.shadowBlur;
            this.ctx.shadowOffsetX = textConfig.shadowOffsetX;
            this.ctx.shadowOffsetY = textConfig.shadowOffsetY;

            // 添加发光效果
            this.ctx.shadowColor = `rgba(255, 215, 0, ${this.titleGlow})`;
            this.ctx.shadowBlur = 15;
            this.ctx.shadowOffsetX = 0;
            this.ctx.shadowOffsetY = 0;

            this.ctx.fillStyle = textConfig.fillColor;
            this.ctx.font = textConfig.fontFamily;
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';

            // 绘制描边
            this.ctx.strokeStyle = textConfig.strokeColor;
            this.ctx.lineWidth = textConfig.strokeWidth;
            this.ctx.strokeText(textConfig.text, 0, 0);

            // 绘制填充
            this.ctx.fillText(textConfig.text, 0, 0);
        } else {
            // 后备方案：使用默认样式
            this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
            this.ctx.shadowBlur = 10;
            this.ctx.shadowOffsetX = 3;
            this.ctx.shadowOffsetY = 3;

            this.ctx.shadowColor = `rgba(255, 215, 0, ${this.titleGlow})`;
            this.ctx.shadowBlur = 15;
            this.ctx.shadowOffsetX = 0;
            this.ctx.shadowOffsetY = 0;

            this.ctx.fillStyle = '#FFFFFF';
            this.ctx.font = 'bold 48px Arial, "Microsoft YaHei"';
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';

            this.ctx.strokeStyle = '#FF69B4';
            this.ctx.lineWidth = 3;
            this.ctx.strokeText('休闲消消消', 0, 0);
            this.ctx.fillText('休闲消消消', 0, 0);
        }
    }

    // 渲染副标题
    renderSubtitle() {
        this.ctx.save();

        // 获取副标题配置和位置
        const subtitleConfig = this.uiConfig ? this.uiConfig.getSubtitleConfig() : null;
        let subtitlePos;

        if (this.uiConfig) {
            subtitlePos = this.uiConfig.calculateSubtitlePosition(this.canvas.width, this.canvas.height);
        } else {
            // 后备方案
            subtitlePos = {
                x: this.canvas.width / 2,
                y: this.canvas.height / 3 + 80
            };
        }

        if (subtitleConfig) {
            // 使用配置中的动画参数
            const subtitleOffset = Math.sin(this.animationTime * subtitleConfig.floatSpeed) * subtitleConfig.floatAmplitude;
            const subtitleAlpha = subtitleConfig.alphaMin + Math.sin(this.animationTime * subtitleConfig.alphaSpeed) * (subtitleConfig.alphaMax - subtitleConfig.alphaMin);

            this.ctx.globalAlpha = subtitleAlpha;
            this.ctx.shadowColor = subtitleConfig.shadowColor;
            this.ctx.shadowBlur = subtitleConfig.shadowBlur;
            this.ctx.shadowOffsetX = subtitleConfig.shadowOffsetX;
            this.ctx.shadowOffsetY = subtitleConfig.shadowOffsetY;

            this.ctx.fillStyle = subtitleConfig.color;
            this.ctx.font = subtitleConfig.fontFamily;
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';
            this.ctx.fillText(subtitleConfig.text, subtitlePos.x, subtitlePos.y + subtitleOffset);
        } else {
            // 后备方案：使用默认样式
            const subtitleOffset = Math.sin(this.animationTime * 1.5) * 2;
            const subtitleAlpha = 0.8 + Math.sin(this.animationTime * 3) * 0.2;

            this.ctx.globalAlpha = subtitleAlpha;
            this.ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
            this.ctx.shadowBlur = 5;
            this.ctx.shadowOffsetX = 2;
            this.ctx.shadowOffsetY = 2;

            this.ctx.fillStyle = '#FFD700';
            this.ctx.font = 'bold 24px Arial, "Microsoft YaHei"';
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';
            this.ctx.fillText('萌宠消除大作战', subtitlePos.x, subtitlePos.y + subtitleOffset);
        }

        this.ctx.restore();
    }
    
    renderButtons() {
        this.buttons.forEach((button, index) => {
            // 移除按钮动画效果，使用固定位置
            const buttonY = button.y;
            const cornerRadius = 15; // 圆角半径

            this.ctx.save();
            this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
            this.ctx.shadowBlur = 8;
            this.ctx.shadowOffsetX = 2;
            this.ctx.shadowOffsetY = 2;

            // 绘制圆角按钮背景
            this.ctx.fillStyle = button.color;
            this.drawRoundedRect(button.x, buttonY, button.width, button.height, cornerRadius);
            this.ctx.fill();

            this.ctx.restore();

            // 绘制圆角按钮边框
            this.ctx.strokeStyle = button.hoverColor;
            this.ctx.lineWidth = 3;
            this.drawRoundedRect(button.x, buttonY, button.width, button.height, cornerRadius);
            this.ctx.stroke();

            this.ctx.fillStyle = '#FFFFFF';
            this.ctx.font = 'bold 20px Arial, "Microsoft YaHei"';
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';
            this.ctx.fillText(
                button.text,
                button.x + button.width / 2,
                buttonY + button.height / 2
            );
        });
    }

    // 绘制圆角矩形
    drawRoundedRect(x, y, width, height, radius) {
        this.ctx.beginPath();
        this.ctx.moveTo(x + radius, y);
        this.ctx.lineTo(x + width - radius, y);
        this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        this.ctx.lineTo(x + width, y + height - radius);
        this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        this.ctx.lineTo(x + radius, y + height);
        this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        this.ctx.lineTo(x, y + radius);
        this.ctx.quadraticCurveTo(x, y, x + radius, y);
        this.ctx.closePath();
    }
    
    renderVersionInfo() {
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
        this.ctx.font = '14px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'right';
        this.ctx.textBaseline = 'bottom';
        this.ctx.fillText('v1.0.0', this.canvas.width - 20, this.canvas.height - 20);
    }
    
    setupTouchEvents() {
        console.log('设置触摸事件监听器...');
        
        if (typeof tt !== 'undefined') {
            console.log('使用抖音小游戏触摸事件API');
            
            this.touchStartHandler = (e) => {
                console.log('触摸开始事件:', e);
                if (e.touches && e.touches.length > 0) {
                    const touch = e.touches[0];
                    this.handleTouchStart(touch.clientX, touch.clientY);
                }
            };
            
            tt.onTouchStart(this.touchStartHandler);
            console.log('抖音小游戏触摸事件监听器设置完成');
        } else {
            console.warn('非抖音小游戏环境，跳过触摸事件设置');
        }
    }
    
    destroy() {
        console.log('清理SimpleMainPage资源');
        if (typeof tt !== 'undefined' && this.touchStartHandler) {
            tt.offTouchStart(this.touchStartHandler);
        }
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SimpleMainPage;
} else {
    window.SimpleMainPage = SimpleMainPage;
}