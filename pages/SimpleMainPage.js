/**
 * 简化的主页面类 - 内联定义避免模块加载问题
 */
class SimpleMainPage {
    constructor(gameManager) {
        this.gameManager = gameManager;
        this.canvas = gameManager.canvas;
        this.ctx = gameManager.ctx;

        // 按钮配置
        this.buttons = [
            {
                id: 'start',
                text: '开始游戏',
                x: 0,
                y: 0,
                width: 200,
                height: 60,
                color: '#4CAF50',
                hoverColor: '#45a049'
            },
            {
                id: 'rank',
                text: '排行榜',
                x: 0,
                y: 0,
                width: 200,
                height: 60,
                color: '#2196F3',
                hoverColor: '#1976D2'
            },
            {
                id: 'setting',
                text: '设置',
                x: 0,
                y: 0,
                width: 200,
                height: 60,
                color: '#FF9800',
                hoverColor: '#F57C00'
            }
        ];

        // 动画状态
        this.titleAnimation = 0;
        this.animationTime = 0;
        this.titleFloatOffset = 0;
        this.titleRotation = 0;
        this.titleScale = 1;
        this.titleGlow = 0;

        // 背景效果
        this.backgroundScene = null;

        // 标题图片
        this.titleImage = null;
        this.titleImageLoaded = false;

        // UI配置
        this.uiConfig = null;

        // 背景元素
        this.backgroundElements = {
            stars: [],
            clouds: [],
            meteors: [],
            rainbows: []
        };

        console.log('SimpleMainPage初始化完成');
    }
    
    init() {
        console.log('初始化主页面');
        this.initUIConfig();
        this.calculateButtonPositions();
        this.initBackgroundEffects();
        this.initBackgroundElements();
        this.loadTitleImage();
        this.setupTouchEvents();
        console.log('主页面初始化完成');
    }

    // 初始化UI配置
    initUIConfig() {
        try {
            if (typeof GameUIConfig !== 'undefined') {
                this.uiConfig = new GameUIConfig();
                console.log('主页UI配置初始化成功');
            } else {
                console.warn('GameUIConfig未加载，使用默认配置');
                this.uiConfig = null;
            }
        } catch (error) {
            console.error('初始化UI配置时出错:', error);
            this.uiConfig = null;
        }
    }

    // 加载标题图片
    loadTitleImage() {
        try {
            this.titleImage = new Image();

            // 使用配置中的图片路径
            if (this.uiConfig && this.uiConfig.images && this.uiConfig.images.title) {
                this.titleImage.src = this.uiConfig.images.title;
                console.log('使用配置中的标题图片路径:', this.uiConfig.images.title);
            } else {
                // 后备方案
                this.titleImage.src = 'images/title.jpg';
                console.log('使用默认标题图片路径: images/title.jpg');
            }

            this.titleImage.onload = () => {
                this.titleImageLoaded = true;
                console.log('标题图片加载成功');
            };

            this.titleImage.onerror = () => {
                console.warn('标题图片加载失败，将使用文字标题');
                this.titleImageLoaded = false;
            };
        } catch (error) {
            console.error('加载标题图片时出错:', error);
            this.titleImageLoaded = false;
        }
    }

    // 初始化背景效果
    initBackgroundEffects() {
        try {
            if (typeof BackgroundUtils !== 'undefined') {
                this.backgroundScene = BackgroundUtils.createBackgroundScene(
                    this.canvas.width,
                    this.canvas.height
                );
                console.log('背景效果初始化成功');
            } else {
                console.warn('BackgroundUtils未加载，使用简化背景');
                this.backgroundScene = null;
            }
        } catch (error) {
            console.warn('背景效果初始化失败:', error);
            this.backgroundScene = null;
        }
    }

    // 初始化背景元素
    initBackgroundElements() {
        const bgConfig = this.uiConfig ? this.uiConfig.getMainPageBackgroundConfig() : null;
        if (!bgConfig) return;

        // 初始化星星
        if (bgConfig.stars.enabled) {
            this.backgroundElements.stars = [];
            for (let i = 0; i < bgConfig.stars.count; i++) {
                this.backgroundElements.stars.push({
                    x: Math.random() * this.canvas.width,
                    y: Math.random() * this.canvas.height,
                    size: bgConfig.stars.minSize + Math.random() * (bgConfig.stars.maxSize - bgConfig.stars.minSize),
                    color: bgConfig.stars.colors[Math.floor(Math.random() * bgConfig.stars.colors.length)],
                    twinkle: Math.random() * Math.PI * 2,
                    twinkleSpeed: bgConfig.stars.twinkleSpeed + Math.random() * 0.5
                });
            }
        }

        // 初始化云朵
        if (bgConfig.clouds.enabled) {
            this.backgroundElements.clouds = [];
            for (let i = 0; i < bgConfig.clouds.count; i++) {
                this.backgroundElements.clouds.push({
                    x: Math.random() * this.canvas.width,
                    y: Math.random() * this.canvas.height * 0.6, // 云朵在上半部分
                    size: bgConfig.clouds.minSize + Math.random() * (bgConfig.clouds.maxSize - bgConfig.clouds.minSize),
                    speed: bgConfig.clouds.speed + Math.random() * 0.2,
                    opacity: bgConfig.clouds.opacity
                });
            }
        }

        // 初始化彩虹（只有一个大彩虹）
        if (bgConfig.rainbow.enabled) {
            this.backgroundElements.rainbows = [];
            this.backgroundElements.rainbows.push({
                x: this.canvas.width * bgConfig.rainbow.x,
                y: this.canvas.height * bgConfig.rainbow.y,
                radius: bgConfig.rainbow.radius,
                animationOffset: 0
            });
        }

        // 初始化流星数组
        this.backgroundElements.meteors = [];
        this.lastMeteorTime = 0;

        // 太阳和彩虹桥不需要初始化数组，它们是固定位置的元素
    }

    calculateButtonPositions() {
        const centerX = this.canvas.width / 2;
        const startY = this.canvas.height / 2 + 50;
        const spacing = 80;
        
        this.buttons.forEach((button, index) => {
            button.x = centerX - button.width / 2;
            button.y = startY + index * spacing;
        });
    }
    
    handleTouchStart(x, y) {
        this.buttons.forEach(button => {
            if (this.isPointInButton(x, y, button)) {
                this.handleButtonClick(button.id);
            }
        });
    }
    
    isPointInButton(x, y, button) {
        return x >= button.x && x <= button.x + button.width &&
               y >= button.y && y <= button.y + button.height;
    }
    
    handleButtonClick(buttonId) {
        console.log(`按钮点击: ${buttonId}`);
        
        switch (buttonId) {
            case 'start':
                const levelConfig = {
                    targetScore: 1000,
                    name: '萌宠新手村',
                    level: 1
                };
                this.gameManager.switchToPage('game', levelConfig);
                break;
            case 'rank':
                this.gameManager.switchToPage('rank');
                break;
            case 'setting':
                this.gameManager.switchToPage('setting');
                break;
            default:
                console.warn(`未知按钮: ${buttonId}`);
        }
    }

    // 更新标题动画状态
    updateTitleAnimation() {
        // 获取动画配置
        const titleConfig = this.uiConfig ? this.uiConfig.getTitleImageConfig() : null;

        if (titleConfig) {
            // 使用配置中的动画参数
            this.titleFloatOffset = Math.sin(this.animationTime * titleConfig.floatSpeed) * titleConfig.floatAmplitude;
            this.titleRotation = Math.sin(this.animationTime * titleConfig.rotationSpeed) * titleConfig.rotationAmplitude;
            this.titleScale = 1 + Math.sin(this.animationTime * titleConfig.scaleSpeed) * titleConfig.scaleAmplitude;
            this.titleGlow = titleConfig.glowMin + Math.sin(this.animationTime * titleConfig.glowSpeed) * (titleConfig.glowMax - titleConfig.glowMin);
        } else {
            // 后备方案：使用默认动画参数
            this.titleFloatOffset = Math.sin(this.animationTime * 0.8) * 8;
            this.titleRotation = Math.sin(this.animationTime * 0.6) * 0.02;
            this.titleScale = 1 + Math.sin(this.animationTime * 1.2) * 0.03;
            this.titleGlow = 0.3 + Math.sin(this.animationTime * 2) * 0.2;
        }

        // 保持原有的titleAnimation用于兼容
        this.titleAnimation = this.animationTime * 0.02;
    }

    // 更新背景元素
    updateBackgroundElements() {
        const bgConfig = this.uiConfig ? this.uiConfig.getMainPageBackgroundConfig() : null;
        if (!bgConfig) return;

        // 更新星星闪烁
        this.backgroundElements.stars.forEach(star => {
            star.twinkle += star.twinkleSpeed * 0.016;
        });

        // 更新云朵位置
        this.backgroundElements.clouds.forEach(cloud => {
            cloud.x += cloud.speed;
            if (cloud.x > this.canvas.width + cloud.size) {
                cloud.x = -cloud.size;
                cloud.y = Math.random() * this.canvas.height * 0.6;
            }
        });

        // 更新彩虹动画
        this.backgroundElements.rainbows.forEach(rainbow => {
            rainbow.animationOffset += bgConfig.rainbow.animationSpeed * 0.016;
        });

        // 更新流星
        this.backgroundElements.meteors = this.backgroundElements.meteors.filter(meteor => {
            meteor.x += meteor.vx;
            meteor.y += meteor.vy;
            meteor.life -= 0.016;
            return meteor.life > 0 && meteor.x < this.canvas.width + 100 && meteor.y < this.canvas.height + 100;
        });

        // 生成新流星
        if (bgConfig.meteors.enabled && Date.now() - this.lastMeteorTime > bgConfig.meteors.spawnInterval) {
            if (this.backgroundElements.meteors.length < bgConfig.meteors.count) {
                this.createMeteor(bgConfig.meteors);
                this.lastMeteorTime = Date.now();
            }
        }
    }

    // 创建流星
    createMeteor(meteorConfig) {
        const startX = -50;
        const startY = Math.random() * this.canvas.height * 0.5;
        const angle = Math.PI / 6 + Math.random() * Math.PI / 6; // 30-60度角

        // 随机选择流星颜色
        let meteorColor;
        if (meteorConfig.colors && Array.isArray(meteorConfig.colors)) {
            meteorColor = meteorConfig.colors[Math.floor(Math.random() * meteorConfig.colors.length)];
        } else {
            meteorColor = meteorConfig.color || '#FFD700'; // 后备颜色
        }

        this.backgroundElements.meteors.push({
            x: startX,
            y: startY,
            vx: Math.cos(angle) * meteorConfig.speed,
            vy: Math.sin(angle) * meteorConfig.speed,
            life: 3, // 3秒生命周期
            length: meteorConfig.length,
            color: meteorColor,
            glowColor: meteorConfig.glowColor
        });
    }

    update() {
        this.animationTime += 0.016; // 60fps

        // 更新标题动画状态
        this.updateTitleAnimation();

        // 更新背景元素
        this.updateBackgroundElements();

        // 更新背景效果
        if (this.backgroundScene && typeof BackgroundUtils !== 'undefined') {
            try {
                BackgroundUtils.updateBackgroundScene(this.backgroundScene, this.canvas.width, this.canvas.height);
            } catch (error) {
                console.warn('背景更新失败:', error);
            }
        }
    }
    
    render() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        this.renderBackground();

        // 渲染背景元素
        this.renderBackgroundElements();

        this.renderTitle();
        this.renderButtons();
        this.renderVersionInfo();
    }
    
    renderBackground() {
        // 渲染紫粉色可爱风渐变背景
        const bgConfig = this.uiConfig ? this.uiConfig.getMainPageBackgroundConfig() : null;

        if (bgConfig && bgConfig.gradient) {
            this.renderGradientBackground(bgConfig.gradient);
        } else {
            // 后备方案：使用BackgroundUtils
            if (this.backgroundScene && typeof BackgroundUtils !== 'undefined') {
                try {
                    this.renderWarmCuteBackground();
                    BackgroundUtils.renderRainbow(this.ctx, this.backgroundScene.rainbow);
                    BackgroundUtils.renderStars(this.ctx, this.backgroundScene.stars);

                    // 渲染浮动粒子
                    BackgroundUtils.renderFloatingParticles(this.ctx, this.backgroundScene.particles);
                } catch (error) {
                    console.warn('背景渲染失败:', error);
                    this.renderSimpleWarmBackground();
                }
            } else {
                this.renderSimpleWarmBackground();
            }
        }
    }

    // 渲染暖系可爱风背景
    renderWarmCuteBackground() {
        // 暖系渐变背景
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        gradient.addColorStop(0, '#FFE5B4');  // 桃色
        gradient.addColorStop(0.3, '#FFCCCB'); // 浅粉色
        gradient.addColorStop(0.6, '#FFB6C1'); // 淡粉色
        gradient.addColorStop(1, '#FFA07A');   // 浅橙色

        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // 添加可爱的装饰圆点
        this.ctx.save();
        for (let i = 0; i < 15; i++) {
            const x = (this.canvas.width / 15) * i + Math.sin(this.animationTime + i) * 20;
            const y = this.canvas.height * 0.2 + Math.cos(this.animationTime + i * 0.5) * 30;
            const radius = 3 + Math.sin(this.animationTime * 2 + i) * 2;
            const alpha = 0.1 + Math.sin(this.animationTime + i) * 0.05;

            this.ctx.globalAlpha = alpha;
            this.ctx.fillStyle = '#FFFFFF';
            this.ctx.beginPath();
            this.ctx.arc(x, y, radius, 0, Math.PI * 2);
            this.ctx.fill();
        }
        this.ctx.restore();
    }

    // 简化的暖系背景
    renderSimpleWarmBackground() {
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        gradient.addColorStop(0, '#FFE5B4');
        gradient.addColorStop(0.5, '#FFCCCB');
        gradient.addColorStop(1, '#FFA07A');

        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }
    
    renderTitle() {
        // 使用配置计算位置
        let titlePos;
        if (this.uiConfig) {
            titlePos = this.uiConfig.calculateTitleImagePosition(this.canvas.width, this.canvas.height);
        } else {
            // 后备方案
            titlePos = {
                x: this.canvas.width / 2,
                y: this.canvas.height / 3
            };
        }

        this.ctx.save();

        // 应用浮动和旋转变换
        this.ctx.translate(titlePos.x, titlePos.y + this.titleFloatOffset);
        this.ctx.rotate(this.titleRotation);
        this.ctx.scale(this.titleScale, this.titleScale);

        if (this.titleImageLoaded && this.titleImage) {
            // 渲染标题图片
            this.renderTitleImage();
        } else {
            // 后备方案：渲染文字标题
            this.renderTextTitle();
        }

        this.ctx.restore();

        // 渲染副标题（不受主标题动画影响）
        this.renderSubtitle();
    }

    // 渲染标题图片
    renderTitleImage() {
        // 添加发光效果
        this.ctx.shadowColor = `rgba(255, 215, 0, ${this.titleGlow})`;
        this.ctx.shadowBlur = 20;
        this.ctx.shadowOffsetX = 0;
        this.ctx.shadowOffsetY = 0;

        // 使用配置计算图片尺寸
        let imageSize;
        if (this.uiConfig) {
            imageSize = this.uiConfig.calculateTitleImageSize(
                this.canvas.width,
                this.titleImage.width,
                this.titleImage.height
            );
        } else {
            // 后备方案：使用默认计算
            const maxWidth = this.canvas.width * 0.6;
            const maxHeight = 120;

            let imageWidth = this.titleImage.width;
            let imageHeight = this.titleImage.height;

            if (imageWidth > maxWidth) {
                const ratio = maxWidth / imageWidth;
                imageWidth = maxWidth;
                imageHeight = imageHeight * ratio;
            }

            if (imageHeight > maxHeight) {
                const ratio = maxHeight / imageHeight;
                imageHeight = maxHeight;
                imageWidth = imageWidth * ratio;
            }

            imageSize = { width: imageWidth, height: imageHeight };
        }

        // 绘制图片（以原点为中心）
        this.ctx.drawImage(
            this.titleImage,
            -imageSize.width / 2,
            -imageSize.height / 2,
            imageSize.width,
            imageSize.height
        );
    }

    // 渲染文字标题（后备方案）
    renderTextTitle() {
        // 获取文字标题配置
        const textConfig = this.uiConfig ? this.uiConfig.getTextTitleConfig() : null;

        if (textConfig) {
            // 使用配置中的样式
            // 添加阴影效果
            this.ctx.shadowColor = textConfig.shadowColor;
            this.ctx.shadowBlur = textConfig.shadowBlur;
            this.ctx.shadowOffsetX = textConfig.shadowOffsetX;
            this.ctx.shadowOffsetY = textConfig.shadowOffsetY;

            // 添加发光效果
            this.ctx.shadowColor = `rgba(255, 215, 0, ${this.titleGlow})`;
            this.ctx.shadowBlur = 15;
            this.ctx.shadowOffsetX = 0;
            this.ctx.shadowOffsetY = 0;

            this.ctx.fillStyle = textConfig.fillColor;
            this.ctx.font = textConfig.fontFamily;
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';

            // 绘制描边
            this.ctx.strokeStyle = textConfig.strokeColor;
            this.ctx.lineWidth = textConfig.strokeWidth;
            this.ctx.strokeText(textConfig.text, 0, 0);

            // 绘制填充
            this.ctx.fillText(textConfig.text, 0, 0);
        } else {
            // 后备方案：使用默认样式
            this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
            this.ctx.shadowBlur = 10;
            this.ctx.shadowOffsetX = 3;
            this.ctx.shadowOffsetY = 3;

            this.ctx.shadowColor = `rgba(255, 215, 0, ${this.titleGlow})`;
            this.ctx.shadowBlur = 15;
            this.ctx.shadowOffsetX = 0;
            this.ctx.shadowOffsetY = 0;

            this.ctx.fillStyle = '#FFFFFF';
            this.ctx.font = 'bold 48px Arial, "Microsoft YaHei"';
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';

            this.ctx.strokeStyle = '#FF69B4';
            this.ctx.lineWidth = 3;
            this.ctx.strokeText('休闲消消消', 0, 0);
            this.ctx.fillText('休闲消消消', 0, 0);
        }
    }

    // 渲染副标题
    renderSubtitle() {
        this.ctx.save();

        // 获取副标题配置和位置
        const subtitleConfig = this.uiConfig ? this.uiConfig.getSubtitleConfig() : null;
        let subtitlePos;

        if (this.uiConfig) {
            subtitlePos = this.uiConfig.calculateSubtitlePosition(this.canvas.width, this.canvas.height);
        } else {
            // 后备方案
            subtitlePos = {
                x: this.canvas.width / 2,
                y: this.canvas.height / 3 + 80
            };
        }

        if (subtitleConfig) {
            // 使用配置中的动画参数
            const subtitleOffset = Math.sin(this.animationTime * subtitleConfig.floatSpeed) * subtitleConfig.floatAmplitude;
            const subtitleAlpha = subtitleConfig.alphaMin + Math.sin(this.animationTime * subtitleConfig.alphaSpeed) * (subtitleConfig.alphaMax - subtitleConfig.alphaMin);

            this.ctx.globalAlpha = subtitleAlpha;
            this.ctx.shadowColor = subtitleConfig.shadowColor;
            this.ctx.shadowBlur = subtitleConfig.shadowBlur;
            this.ctx.shadowOffsetX = subtitleConfig.shadowOffsetX;
            this.ctx.shadowOffsetY = subtitleConfig.shadowOffsetY;

            this.ctx.fillStyle = subtitleConfig.color;
            this.ctx.font = subtitleConfig.fontFamily;
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';
            this.ctx.fillText(subtitleConfig.text, subtitlePos.x, subtitlePos.y + subtitleOffset);
        } else {
            // 后备方案：使用默认样式
            const subtitleOffset = Math.sin(this.animationTime * 1.5) * 2;
            const subtitleAlpha = 0.8 + Math.sin(this.animationTime * 3) * 0.2;

            this.ctx.globalAlpha = subtitleAlpha;
            this.ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
            this.ctx.shadowBlur = 5;
            this.ctx.shadowOffsetX = 2;
            this.ctx.shadowOffsetY = 2;

            this.ctx.fillStyle = '#FFD700';
            this.ctx.font = 'bold 24px Arial, "Microsoft YaHei"';
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';
            this.ctx.fillText('萌宠消除大作战', subtitlePos.x, subtitlePos.y + subtitleOffset);
        }

        this.ctx.restore();
    }
    
    renderButtons() {
        const buttonConfig = this.uiConfig ? this.uiConfig.getMainPageButtonsConfig() : null;

        this.buttons.forEach((button, index) => {
            this.ctx.save();

            if (buttonConfig) {
                this.renderCuteButton(button, buttonConfig, index);
            } else {
                this.renderDefaultButton(button);
            }

            this.ctx.restore();
        });
    }

    // 渲染简洁可爱风按钮（无蒙版）
    renderCuteButton(button, config, index) {
        const style = config.style;
        const buttonY = button.y;

        this.ctx.save();

        // 绘制阴影
        if (style.shadow) {
            this.ctx.shadowColor = style.shadow.color;
            this.ctx.shadowBlur = style.shadow.blur;
            this.ctx.shadowOffsetX = style.shadow.offsetX;
            this.ctx.shadowOffsetY = style.shadow.offsetY;
        }

        // 绘制按钮背景渐变
        const gradient = this.ctx.createLinearGradient(
            button.x, buttonY,
            button.x, buttonY + button.height
        );

        style.gradient.colors.forEach((color, i) => {
            gradient.addColorStop(i / (style.gradient.colors.length - 1), color);
        });

        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.drawRoundedRect(button.x, buttonY, button.width, button.height, style.borderRadius);
        this.ctx.fill();

        // 重置阴影
        this.ctx.shadowColor = 'transparent';

        // 绘制边框
        if (style.border) {
            this.ctx.strokeStyle = style.border.color;
            this.ctx.lineWidth = style.border.width;
            this.ctx.beginPath();
            this.drawRoundedRect(button.x, buttonY, button.width, button.height, style.borderRadius);
            this.ctx.stroke();
        }

        // 绘制按钮文字
        this.renderButtonText(button, config.text, index, buttonY);

        // 绘制装饰元素
        this.renderButtonDecorations(button, config.decorations, index, buttonY);

        this.ctx.restore();
    }

    // 渲染按钮立体边框
    renderButtonBorders(button, buttonY, style) {
        if (!style.border) return;

        // 绘制外边框（阴影边框）
        if (style.border.outer) {
            this.ctx.strokeStyle = style.border.outer.color;
            this.ctx.lineWidth = style.border.outer.width;
            this.drawRoundedRect(
                button.x + style.border.outer.offset,
                buttonY + style.border.outer.offset,
                button.width - style.border.outer.offset * 2,
                button.height - style.border.outer.offset * 2,
                style.borderRadius
            );
            this.ctx.stroke();
        }

        // 绘制主边框
        if (style.border.main) {
            this.ctx.strokeStyle = style.border.main.color;
            this.ctx.lineWidth = style.border.main.width;
            this.drawRoundedRect(button.x, buttonY, button.width, button.height, style.borderRadius);
            this.ctx.stroke();
        }

        // 绘制内边框（高光边框）
        if (style.border.inner) {
            this.ctx.strokeStyle = style.border.inner.color;
            this.ctx.lineWidth = style.border.inner.width;
            this.drawRoundedRect(
                button.x + style.border.inner.offset,
                buttonY + style.border.inner.offset,
                button.width - style.border.inner.offset * 2,
                button.height - style.border.inner.offset * 2,
                style.borderRadius - style.border.inner.offset
            );
            this.ctx.stroke();
        }
    }

    // 渲染按钮内阴影
    renderButtonInnerShadow(button, buttonY, style) {
        if (!style.shadow || !style.shadow.inner) return;

        this.ctx.save();

        const innerShadow = style.shadow.inner;

        // 创建内阴影效果
        this.ctx.globalCompositeOperation = 'multiply';
        this.ctx.shadowColor = innerShadow.color;
        this.ctx.shadowBlur = innerShadow.blur;
        this.ctx.shadowOffsetX = innerShadow.offsetX;
        this.ctx.shadowOffsetY = innerShadow.offsetY;

        // 绘制内阴影
        this.ctx.strokeStyle = 'transparent';
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.drawRoundedRect(
            button.x + 4, buttonY + 4,
            button.width - 8, button.height - 8,
            style.borderRadius - 4
        );
        this.ctx.stroke();

        this.ctx.restore();
    }

    // 渲染按钮内发光
    renderButtonInnerGlow(button, buttonY, style) {
        if (!style.innerGlow) return;

        this.ctx.save();

        const innerGlow = style.innerGlow;

        // 创建内发光效果
        this.ctx.globalCompositeOperation = 'screen';
        this.ctx.globalAlpha = innerGlow.intensity || 0.8;
        this.ctx.shadowColor = innerGlow.color;
        this.ctx.shadowBlur = innerGlow.blur;
        this.ctx.shadowOffsetX = 0;
        this.ctx.shadowOffsetY = 0;

        // 绘制内发光
        this.ctx.strokeStyle = 'transparent';
        this.ctx.lineWidth = 1;
        this.ctx.beginPath();
        this.drawRoundedRect(
            button.x + 5, buttonY + 5,
            button.width - 10, button.height - 10,
            style.borderRadius - 5
        );
        this.ctx.stroke();

        this.ctx.restore();
    }

    // 渲染按钮顶部高光
    renderButtonTopHighlight(button, buttonY, style) {
        if (!style.topHighlight || !style.topHighlight.enabled) return;

        this.ctx.save();

        const highlight = style.topHighlight;
        const highlightHeight = button.height * highlight.height;

        // 创建顶部高光渐变
        const highlightGradient = this.ctx.createLinearGradient(
            button.x, buttonY,
            button.x, buttonY + highlightHeight
        );
        highlightGradient.addColorStop(0, highlight.color);
        highlightGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

        this.ctx.globalCompositeOperation = 'screen';
        this.ctx.fillStyle = highlightGradient;
        this.ctx.beginPath();
        this.drawRoundedRect(
            button.x + 3, buttonY + 3,
            button.width - 6, highlightHeight,
            style.borderRadius - 3
        );
        this.ctx.fill();

        this.ctx.restore();
    }

    // 渲染按钮文字
    renderButtonText(button, textConfig, index, buttonY) {
        this.ctx.save();

        // 设置文字样式
        this.ctx.fillStyle = textConfig.color;
        this.ctx.font = textConfig.fontFamily;
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';

        // 文字阴影
        if (textConfig.shadow) {
            this.ctx.shadowColor = textConfig.shadow.color;
            this.ctx.shadowBlur = textConfig.shadow.blur;
            this.ctx.shadowOffsetX = textConfig.shadow.offsetX;
            this.ctx.shadowOffsetY = textConfig.shadow.offsetY;
        }

        // 文字描边
        if (textConfig.stroke) {
            this.ctx.strokeStyle = textConfig.stroke.color;
            this.ctx.lineWidth = textConfig.stroke.width;
            this.ctx.strokeText(
                button.text,
                button.x + button.width / 2,
                buttonY + button.height / 2
            );
        }

        // 绘制文字
        this.ctx.fillText(
            button.text,
            button.x + button.width / 2,
            buttonY + button.height / 2
        );

        this.ctx.restore();
    }

    // 渲染按钮装饰元素
    renderButtonDecorations(button, decorations, index, buttonY) {
        if (!decorations) return;

        // 渲染星星装饰
        if (decorations.stars && decorations.stars.enabled) {
            this.renderButtonStars(button, decorations.stars, index, buttonY);
        }

        // 渲染爱心装饰
        if (decorations.hearts && decorations.hearts.enabled) {
            this.renderButtonHearts(button, decorations.hearts, index, buttonY);
        }
    }

    // 渲染按钮星星装饰
    renderButtonStars(button, starConfig, index, buttonY) {
        this.ctx.save();

        for (let i = 0; i < starConfig.count; i++) {
            const angle = (i * 2 * Math.PI) / starConfig.count + this.animationTime * starConfig.animation.speed;
            const radius = button.width * 0.6;
            const x = button.x + button.width / 2 + Math.cos(angle) * radius;
            const y = buttonY + button.height / 2 + Math.sin(angle) * radius;

            const color = starConfig.colors[i % starConfig.colors.length];

            // 闪烁效果
            if (starConfig.animation.twinkle) {
                const twinkle = Math.sin(this.animationTime * starConfig.animation.speed + i) * 0.5 + 0.5;
                this.ctx.globalAlpha = twinkle;
            }

            this.ctx.fillStyle = color;
            this.ctx.shadowColor = color;
            this.ctx.shadowBlur = starConfig.size * 2;

            this.renderStar(x, y, starConfig.size);
        }

        this.ctx.restore();
    }

    // 渲染按钮爱心装饰
    renderButtonHearts(button, heartConfig, index, buttonY) {
        this.ctx.save();

        for (let i = 0; i < heartConfig.count; i++) {
            const side = i % 2 === 0 ? -1 : 1;
            const offsetX = side * (button.width * 0.4);
            const offsetY = Math.sin(this.animationTime * heartConfig.animation.speed + i) * 5;

            const x = button.x + button.width / 2 + offsetX;
            const y = buttonY + button.height / 2 + offsetY;

            const color = heartConfig.colors[i % heartConfig.colors.length];

            this.ctx.fillStyle = color;
            this.ctx.shadowColor = color;
            this.ctx.shadowBlur = heartConfig.size;

            this.drawHeart(x, y, heartConfig.size);
        }

        this.ctx.restore();
    }

    // 渲染默认按钮（后备方案）
    renderDefaultButton(button) {
        const buttonY = button.y;
        const cornerRadius = 15;

        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        this.ctx.shadowBlur = 8;
        this.ctx.shadowOffsetX = 2;
        this.ctx.shadowOffsetY = 2;

        // 绘制圆角按钮背景
        this.ctx.fillStyle = button.color;
        this.drawRoundedRect(button.x, buttonY, button.width, button.height, cornerRadius);
        this.ctx.fill();

        // 重置阴影
        this.ctx.shadowColor = 'transparent';

        // 绘制圆角按钮边框
        this.ctx.strokeStyle = button.hoverColor;
        this.ctx.lineWidth = 3;
        this.drawRoundedRect(button.x, buttonY, button.width, button.height, cornerRadius);
        this.ctx.stroke();

        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = 'bold 20px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText(
            button.text,
            button.x + button.width / 2,
            buttonY + button.height / 2
        );
    }

    // 绘制圆角矩形
    drawRoundedRect(x, y, width, height, radius) {
        this.ctx.beginPath();
        this.ctx.moveTo(x + radius, y);
        this.ctx.lineTo(x + width - radius, y);
        this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        this.ctx.lineTo(x + width, y + height - radius);
        this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        this.ctx.lineTo(x + radius, y + height);
        this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        this.ctx.lineTo(x, y + radius);
        this.ctx.quadraticCurveTo(x, y, x + radius, y);
        this.ctx.closePath();
    }

    // 绘制爱心形状
    drawHeart(x, y, size) {
        this.ctx.save();
        this.ctx.translate(x, y);
        this.ctx.scale(size / 10, size / 10);

        this.ctx.beginPath();
        this.ctx.moveTo(0, 3);
        this.ctx.bezierCurveTo(-5, -2, -10, 1, -5, 8);
        this.ctx.bezierCurveTo(0, 12, 0, 12, 0, 12);
        this.ctx.bezierCurveTo(0, 12, 0, 12, 5, 8);
        this.ctx.bezierCurveTo(10, 1, 5, -2, 0, 3);
        this.ctx.fill();

        this.ctx.restore();
    }
    
    renderVersionInfo() {
        // 获取版本信息配置
        const versionConfig = this.uiConfig ? this.uiConfig.getVersionInfoConfig() : null;

        if (!versionConfig || !versionConfig.enabled) {
            // 后备方案：使用默认配置
            this.ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
            this.ctx.font = '14px Arial, "Microsoft YaHei"';
            this.ctx.textAlign = 'right';
            this.ctx.textBaseline = 'bottom';
            this.ctx.fillText('v1.0.0', this.canvas.width - 20, this.canvas.height - 20);
            return;
        }

        this.ctx.save();

        // 使用配置中的样式
        this.ctx.fillStyle = versionConfig.color;
        this.ctx.font = versionConfig.fontFamily;
        this.ctx.textAlign = versionConfig.textAlign;
        this.ctx.textBaseline = versionConfig.textBaseline;

        // 添加阴影效果
        this.ctx.shadowColor = versionConfig.shadowColor;
        this.ctx.shadowBlur = versionConfig.shadowBlur;
        this.ctx.shadowOffsetX = versionConfig.shadowOffsetX;
        this.ctx.shadowOffsetY = versionConfig.shadowOffsetY;

        // 计算位置
        const x = this.canvas.width * versionConfig.x;
        const y = this.canvas.height * versionConfig.y;

        // 绘制版本信息
        this.ctx.fillText(versionConfig.text, x, y);

        this.ctx.restore();
    }
    
    setupTouchEvents() {
        console.log('设置触摸事件监听器...');
        
        if (typeof tt !== 'undefined') {
            console.log('使用抖音小游戏触摸事件API');
            
            this.touchStartHandler = (e) => {
                console.log('触摸开始事件:', e);
                if (e.touches && e.touches.length > 0) {
                    const touch = e.touches[0];
                    this.handleTouchStart(touch.clientX, touch.clientY);
                }
            };
            
            tt.onTouchStart(this.touchStartHandler);
            console.log('抖音小游戏触摸事件监听器设置完成');
        } else {
            console.warn('非抖音小游戏环境，跳过触摸事件设置');
        }
    }
    
    // 渲染渐变背景
    renderGradientBackground(gradientConfig) {
        this.ctx.save();

        let gradient;
        if (gradientConfig.direction === 'radial') {
            gradient = this.ctx.createRadialGradient(
                this.canvas.width / 2, this.canvas.height / 2, 0,
                this.canvas.width / 2, this.canvas.height / 2, Math.max(this.canvas.width, this.canvas.height) / 2
            );
        } else {
            gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        }

        const colors = gradientConfig.colors;
        for (let i = 0; i < colors.length; i++) {
            gradient.addColorStop(i / (colors.length - 1), colors[i]);
        }

        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        this.ctx.restore();
    }

    // 渲染背景元素
    renderBackgroundElements() {
        if (!this.uiConfig) return;

        const bgConfig = this.uiConfig.getMainPageBackgroundConfig();
        if (!bgConfig) return;

        // 渲染太阳
        if (bgConfig.sun && bgConfig.sun.enabled) {
            this.renderSun(bgConfig.sun);
        }

        // 渲染彩虹桥
        if (bgConfig.rainbowBridge && bgConfig.rainbowBridge.enabled) {
            this.renderRainbowBridge(bgConfig.rainbowBridge);
        }

        // 渲染桥梁
        if (bgConfig.bridge && bgConfig.bridge.enabled) {
            this.renderBridge(bgConfig.bridge);
        }

        // 渲染彩虹
        if (bgConfig.rainbow && bgConfig.rainbow.enabled) {
            this.renderRainbows(bgConfig.rainbow);
        }

        // 渲染云朵
        if (bgConfig.clouds && bgConfig.clouds.enabled) {
            this.renderClouds(bgConfig.clouds);
        }

        // 渲染星星
        if (bgConfig.stars && bgConfig.stars.enabled) {
            this.renderStars(bgConfig.stars);
        }

        // 渲染流星
        if (bgConfig.meteors && bgConfig.meteors.enabled) {
            this.renderMeteors(bgConfig.meteors);
        }
    }

    // 渲染带笑脸的太阳
    renderSun(sunConfig) {
        this.ctx.save();

        const sunX = this.canvas.width * sunConfig.x;
        const sunY = this.canvas.height * sunConfig.y;
        const sunSize = sunConfig.size;

        // 渲染太阳光芒（旋转动画）
        this.ctx.translate(sunX, sunY);
        this.ctx.rotate(this.animationTime * sunConfig.rotationSpeed);

        // 绘制光芒
        this.ctx.strokeStyle = sunConfig.rayColor;
        this.ctx.lineWidth = 3;
        this.ctx.shadowColor = sunConfig.rayColor;
        this.ctx.shadowBlur = 10;

        for (let i = 0; i < sunConfig.rayCount; i++) {
            const angle = (i * 2 * Math.PI) / sunConfig.rayCount;
            const startRadius = sunSize / 2 + 5;
            const endRadius = startRadius + sunConfig.rayLength;

            this.ctx.beginPath();
            this.ctx.moveTo(
                Math.cos(angle) * startRadius,
                Math.sin(angle) * startRadius
            );
            this.ctx.lineTo(
                Math.cos(angle) * endRadius,
                Math.sin(angle) * endRadius
            );
            this.ctx.stroke();
        }

        // 重置变换
        this.ctx.setTransform(1, 0, 0, 1, 0, 0);

        // 绘制太阳主体
        this.ctx.fillStyle = sunConfig.color;
        this.ctx.shadowColor = sunConfig.color;
        this.ctx.shadowBlur = 20;
        this.ctx.beginPath();
        this.ctx.arc(sunX, sunY, sunSize / 2, 0, Math.PI * 2);
        this.ctx.fill();

        // 绘制笑脸
        this.ctx.shadowBlur = 0;
        const face = sunConfig.face;

        // 绘制眼睛
        this.ctx.fillStyle = face.eyeColor;
        // 左眼
        this.ctx.beginPath();
        this.ctx.arc(sunX - sunSize / 4, sunY - sunSize / 6, face.eyeSize, 0, Math.PI * 2);
        this.ctx.fill();
        // 右眼
        this.ctx.beginPath();
        this.ctx.arc(sunX + sunSize / 4, sunY - sunSize / 6, face.eyeSize, 0, Math.PI * 2);
        this.ctx.fill();

        // 绘制笑脸嘴巴
        this.ctx.strokeStyle = face.mouthColor;
        this.ctx.lineWidth = 4;
        this.ctx.beginPath();
        this.ctx.arc(sunX, sunY + sunSize / 8, face.mouthWidth / 2, 0, Math.PI);
        this.ctx.stroke();

        this.ctx.restore();
    }

    // 渲染彩虹桥
    renderRainbowBridge(bridgeConfig) {
        this.ctx.save();

        const bridgeX = this.canvas.width * bridgeConfig.x;
        const bridgeY = this.canvas.height * bridgeConfig.y;

        this.ctx.globalAlpha = bridgeConfig.opacity;
        this.ctx.translate(bridgeX, bridgeY);

        const colors = bridgeConfig.colors;
        const bandWidth = bridgeConfig.bandWidth;

        // 绘制彩虹桥（半圆弧形）
        for (let i = 0; i < colors.length; i++) {
            this.ctx.beginPath();
            this.ctx.strokeStyle = colors[i];
            this.ctx.lineWidth = bandWidth;

            const radius = bridgeConfig.height - i * bandWidth;
            this.ctx.arc(0, 0, radius, Math.PI, 0); // 从π到0，绘制上半圆
            this.ctx.stroke();
        }

        this.ctx.restore();
    }

    // 渲染桥梁
    renderBridge(bridgeConfig) {
        this.ctx.save();

        const bridgeX = this.canvas.width * bridgeConfig.x;
        const bridgeY = this.canvas.height * bridgeConfig.y;
        const bridgeWidth = bridgeConfig.width;
        const bridgeHeight = bridgeConfig.height;

        // 绘制桥梁主体
        this.ctx.fillStyle = bridgeConfig.color;
        this.ctx.shadowColor = bridgeConfig.shadowColor;
        this.ctx.shadowBlur = 5;
        this.ctx.shadowOffsetY = 3;

        // 桥梁主体（矩形）
        this.ctx.fillRect(
            bridgeX - bridgeWidth / 2,
            bridgeY - bridgeHeight / 2,
            bridgeWidth,
            bridgeHeight
        );

        // 绘制桥墩
        if (bridgeConfig.pillars.enabled) {
            this.ctx.shadowBlur = 0;
            this.ctx.fillStyle = bridgeConfig.pillars.color;

            const pillarCount = bridgeConfig.pillars.count;
            const pillarWidth = bridgeConfig.pillars.width;
            const pillarHeight = bridgeConfig.pillars.height;

            // 均匀分布桥墩
            for (let i = 0; i < pillarCount; i++) {
                const pillarX = bridgeX - bridgeWidth / 2 + (bridgeWidth / (pillarCount + 1)) * (i + 1);
                const pillarY = bridgeY + bridgeHeight / 2;

                this.ctx.fillRect(
                    pillarX - pillarWidth / 2,
                    pillarY,
                    pillarWidth,
                    pillarHeight
                );
            }
        }

        this.ctx.restore();
    }

    // 渲染彩虹
    renderRainbows(rainbowConfig) {
        if (!this.backgroundElements.rainbows) return;

        this.ctx.save();
        this.ctx.globalAlpha = rainbowConfig.opacity;

        this.backgroundElements.rainbows.forEach(rainbow => {
            this.ctx.save();
            this.ctx.translate(rainbow.x, rainbow.y);

            const colors = rainbowConfig.colors;
            const bandWidth = rainbowConfig.width;

            // 绘制大彩虹（半圆弧形）
            for (let i = 0; i < colors.length; i++) {
                this.ctx.beginPath();
                this.ctx.strokeStyle = colors[i];
                this.ctx.lineWidth = bandWidth;

                const radius = rainbow.radius - i * bandWidth;
                this.ctx.arc(0, 0, radius, Math.PI, 0); // 从π到0，绘制上半圆
                this.ctx.stroke();
            }

            this.ctx.restore();
        });

        this.ctx.restore();
    }

    // 渲染云朵
    renderClouds(cloudConfig) {
        if (!this.backgroundElements.clouds) return;

        this.ctx.save();
        this.ctx.fillStyle = cloudConfig.color;
        this.ctx.globalAlpha = cloudConfig.opacity;

        this.backgroundElements.clouds.forEach(cloud => {
            this.renderCloud(cloud.x, cloud.y, cloud.size);
        });

        this.ctx.restore();
    }

    // 渲染单个云朵
    renderCloud(x, y, size) {
        this.ctx.save();
        this.ctx.translate(x, y);

        // 云朵由多个圆形组成
        const circles = [
            { x: 0, y: 0, r: size * 0.5 },
            { x: -size * 0.3, y: -size * 0.1, r: size * 0.4 },
            { x: size * 0.3, y: -size * 0.1, r: size * 0.4 },
            { x: -size * 0.5, y: size * 0.1, r: size * 0.3 },
            { x: size * 0.5, y: size * 0.1, r: size * 0.3 }
        ];

        this.ctx.beginPath();
        circles.forEach(circle => {
            this.ctx.arc(circle.x, circle.y, circle.r, 0, Math.PI * 2);
        });
        this.ctx.fill();

        this.ctx.restore();
    }

    // 渲染星星
    renderStars(starConfig) {
        if (!this.backgroundElements.stars) return;

        this.backgroundElements.stars.forEach(star => {
            this.ctx.save();

            // 闪烁效果
            const twinkle = Math.sin(star.twinkle) * 0.5 + 0.5;
            this.ctx.globalAlpha = twinkle;

            this.ctx.fillStyle = star.color;
            this.ctx.shadowColor = star.color;
            this.ctx.shadowBlur = star.size * 2;

            // 绘制星星（五角星）
            this.renderStar(star.x, star.y, star.size);

            this.ctx.restore();
        });
    }

    // 渲染五角星
    renderStar(x, y, size) {
        this.ctx.save();
        this.ctx.translate(x, y);

        this.ctx.beginPath();
        for (let i = 0; i < 5; i++) {
            const angle = (i * 4 * Math.PI) / 5;
            const x1 = Math.cos(angle) * size;
            const y1 = Math.sin(angle) * size;

            if (i === 0) {
                this.ctx.moveTo(x1, y1);
            } else {
                this.ctx.lineTo(x1, y1);
            }
        }
        this.ctx.closePath();
        this.ctx.fill();

        this.ctx.restore();
    }

    // 渲染流星
    renderMeteors(meteorConfig) {
        if (!this.backgroundElements.meteors) return;

        this.backgroundElements.meteors.forEach(meteor => {
            this.ctx.save();

            // 流星尾巴渐变
            const gradient = this.ctx.createLinearGradient(
                meteor.x, meteor.y,
                meteor.x - meteor.vx * meteor.length / meteorConfig.speed,
                meteor.y - meteor.vy * meteor.length / meteorConfig.speed
            );
            gradient.addColorStop(0, meteor.color);
            gradient.addColorStop(1, 'transparent');

            this.ctx.strokeStyle = gradient;
            this.ctx.lineWidth = 3;
            this.ctx.shadowColor = meteor.glowColor;
            this.ctx.shadowBlur = 10;

            this.ctx.beginPath();
            this.ctx.moveTo(meteor.x, meteor.y);
            this.ctx.lineTo(
                meteor.x - meteor.vx * meteor.length / meteorConfig.speed,
                meteor.y - meteor.vy * meteor.length / meteorConfig.speed
            );
            this.ctx.stroke();

            // 流星头部
            this.ctx.fillStyle = meteor.color;
            this.ctx.beginPath();
            this.ctx.arc(meteor.x, meteor.y, 2, 0, Math.PI * 2);
            this.ctx.fill();

            this.ctx.restore();
        });
    }

    destroy() {
        console.log('清理SimpleMainPage资源');
        if (typeof tt !== 'undefined' && this.touchStartHandler) {
            tt.offTouchStart(this.touchStartHandler);
        }
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SimpleMainPage;
} else {
    window.SimpleMainPage = SimpleMainPage;
}