/**
 * 简化的游戏页面类
 */
class SimpleGamePage {
    constructor(gameManager, levelConfig) {
        this.gameManager = gameManager;
        this.canvas = gameManager.canvas;
        this.ctx = gameManager.ctx;
        
        // 关卡配置
        this.levelConfig = levelConfig || {
            targetScore: 1000,
            name: '萌宠新手村',
            level: 1
        };
        
        // 游戏状态
        this.score = 0;
        this.targetScore = this.levelConfig.targetScore;
        this.levelName = this.levelConfig.name;
        this.isGameOver = false;
        this.isLevelComplete = false;
        
        // 网格配置
        this.gridSizeX = 8;
        this.gridSizeY = 10;
        this.blockSize = 50;
        this.grid = [];
        
        // 萌宠类型
        this.animalTypes = ['🐱', '🐶', '🐘', '🦊', '🐸', '🐵', '🐼', '🐰', '🐯'];
        
        // UI按钮
        this.backButton = {
            x: 50,
            y: 50,
            width: 100,
            height: 40,
            text: '返回',
            color: '#FF6B6B'
        };
        
        this.restartButton = {
            x: 0,
            y: 0,
            width: 120,
            height: 40,
            text: '重新开始',
            color: '#4CAF50'
        };
        
        // 动画状态
        this.animationTime = 0;
        
        console.log('SimpleGamePage初始化完成');
    }
    
    init() {
        console.log('初始化游戏页面');
        this.initGrid();
        this.calculateButtonPositions();
        this.setupTouchEvents();
        console.log('游戏页面初始化完成');
    }
    
    initGrid() {
        this.grid = [];
        for (let row = 0; row < this.gridSizeY; row++) {
            this.grid[row] = [];
            for (let col = 0; col < this.gridSizeX; col++) {
                this.grid[row][col] = {
                    type: this.animalTypes[Math.floor(Math.random() * this.animalTypes.length)],
                    x: col * this.blockSize + 100,
                    y: row * this.blockSize + 200
                };
            }
        }
    }
    
    calculateButtonPositions() {
        this.restartButton.x = this.canvas.width - 170;
        this.restartButton.y = 50;
    }
    
    handleTouchStart(x, y) {
        // 检查返回按钮
        if (this.isPointInButton(x, y, this.backButton)) {
            console.log('点击返回按钮');
            this.gameManager.switchToPage('main');
            return;
        }
        
        // 检查重新开始按钮
        if (this.isPointInButton(x, y, this.restartButton)) {
            console.log('点击重新开始按钮');
            this.restartGame();
            return;
        }
        
        // 检查游戏网格点击
        this.handleGridClick(x, y);
    }
    
    handleGridClick(x, y) {
        // 简化的点击处理 - 点击方块增加分数
        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX; col++) {
                const block = this.grid[row][col];
                if (x >= block.x && x <= block.x + this.blockSize &&
                    y >= block.y && y <= block.y + this.blockSize) {
                    
                    // 简单的消除逻辑 - 点击增加分数并重新生成方块
                    this.score += 100;
                    this.grid[row][col].type = this.animalTypes[Math.floor(Math.random() * this.animalTypes.length)];
                    
                    console.log(`点击方块，当前分数: ${this.score}`);
                    
                    // 检查是否达到目标分数
                    if (this.score >= this.targetScore) {
                        this.isLevelComplete = true;
                        console.log('恭喜过关！');
                    }
                    
                    return;
                }
            }
        }
    }
    
    restartGame() {
        this.score = 0;
        this.isGameOver = false;
        this.isLevelComplete = false;
        this.initGrid();
        console.log('游戏重新开始');
    }
    
    isPointInButton(x, y, button) {
        return x >= button.x && x <= button.x + button.width &&
               y >= button.y && y <= button.y + button.height;
    }
    
    update() {
        this.animationTime += 0.02;
    }
    
    render() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        this.renderBackground();
        this.renderUI();
        this.renderGrid();
        this.renderGameStatus();
    }
    
    renderBackground() {
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        gradient.addColorStop(0, '#ff9a9e');
        gradient.addColorStop(0.5, '#fecfef');
        gradient.addColorStop(1, '#fecfef');
        
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }
    
    renderUI() {
        // 渲染返回按钮
        this.ctx.fillStyle = this.backButton.color;
        this.ctx.fillRect(this.backButton.x, this.backButton.y, this.backButton.width, this.backButton.height);
        
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = 'bold 16px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText(
            this.backButton.text,
            this.backButton.x + this.backButton.width / 2,
            this.backButton.y + this.backButton.height / 2
        );
        
        // 渲染重新开始按钮
        // 渲染重新开始按钮
        this.ctx.fillStyle = this.restartButton.color;
        this.ctx.fillRect(this.restartButton.x, this.restartButton.y, this.restartButton.width, this.restartButton.height);
        
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.fillText(
            this.restartButton.text,
            this.restartButton.x + this.restartButton.width / 2,
            this.restartButton.y + this.restartButton.height / 2
        );
        
        // 渲染关卡信息
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = 'bold 24px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(this.levelName, this.canvas.width / 2, 120);
        
        // 渲染分数信息
        this.ctx.font = '20px Arial, "Microsoft YaHei"';
        this.ctx.fillText(`分数: ${this.score} / ${this.targetScore}`, this.canvas.width / 2, 150);
    }
    
    renderGrid() {
        // 渲染游戏网格
        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX; col++) {
                const block = this.grid[row][col];
                
                // 绘制方块背景
                this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                this.ctx.fillRect(block.x, block.y, this.blockSize, this.blockSize);
                
                // 绘制方块边框
                this.ctx.strokeStyle = '#CCCCCC';
                this.ctx.lineWidth = 2;
                this.ctx.strokeRect(block.x, block.y, this.blockSize, this.blockSize);
                
                // 绘制萌宠图标
                this.ctx.fillStyle = '#333333';
                this.ctx.font = '32px Arial';
                this.ctx.textAlign = 'center';
                this.ctx.textBaseline = 'middle';
                this.ctx.fillText(
                    block.type,
                    block.x + this.blockSize / 2,
                    block.y + this.blockSize / 2
                );
            }
        }
    }
    
    renderGameStatus() {
        // 渲染游戏状态提示
        if (this.isLevelComplete) {
            this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
            
            this.ctx.fillStyle = '#FFD700';
            this.ctx.font = 'bold 48px Arial, "Microsoft YaHei"';
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';
            this.ctx.fillText('恭喜过关！', this.canvas.width / 2, this.canvas.height / 2);
            
            this.ctx.fillStyle = '#FFFFFF';
            this.ctx.font = '24px Arial, "Microsoft YaHei"';
            this.ctx.fillText('点击重新开始继续游戏', this.canvas.width / 2, this.canvas.height / 2 + 60);
        } else {
            // 渲染游戏提示
            this.ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            this.ctx.font = '16px Arial, "Microsoft YaHei"';
            this.ctx.textAlign = 'center';
            this.ctx.fillText('点击萌宠方块来消除它们！', this.canvas.width / 2, this.canvas.height - 50);
        }
    }
    
    setupTouchEvents() {
        if (typeof tt !== 'undefined') {
            this.touchStartHandler = (e) => {
                if (e.touches && e.touches.length > 0) {
                    const touch = e.touches[0];
                    this.handleTouchStart(touch.clientX, touch.clientY);
                }
            };
            
            tt.onTouchStart(this.touchStartHandler);
        }
    }
    
    destroy() {
        console.log('清理SimpleGamePage资源');
        if (typeof tt !== 'undefined' && this.touchStartHandler) {
            tt.offTouchStart(this.touchStartHandler);
        }
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SimpleGamePage;
} else {
    window.SimpleGamePage = SimpleGamePage;
}
