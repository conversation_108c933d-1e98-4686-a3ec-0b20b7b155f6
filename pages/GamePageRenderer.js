/**
 * 游戏页面渲染器 - 简洁美化版本
 * 负责所有视觉效果的渲染，无浮动动画
 */
class GamePageRenderer {
    constructor(core) {
        this.core = core;
        this.canvas = core.canvas;
        this.ctx = core.ctx;

        // 获取UI配置
        this.uiConfig = core.uiConfig;

        // 验证UI配置是否可用
        if (!this.uiConfig) {
            console.warn('UI配置不可用，渲染器可能无法正常工作');
        }

        // 动画时间
        this.animationTime = 0;

        // 根据关卡配置颜色主题
        this.initLevelColors();

        // 初始化可爱风背景（延迟初始化）
        this.cuteBackground = null;
        this.backgroundInitialized = false;

        this.colors = {
            background: this.levelColors,
            grid: {
                background: 'rgba(255, 255, 255, 0.8)', // 半透明白色
                border: '#FFFFFF',
                shadow: 'rgba(0, 0, 0, 0.1)'
            },
            animals: {
                'cat': { bg: '#FF6B9D', glow: '#FF8FB3' },
                'dog': { bg: '#4ECDC4', glow: '#6ED5CD' },
                'elephant': { bg: '#45B7D1', glow: '#65C7E1' },
                'fox': { bg: '#96CEB4', glow: '#A6DEC4' },
                'frog': { bg: '#FFEAA7', glow: '#FFEFB7' },
                'monkey': { bg: '#DDA0DD', glow: '#E7B0E7' },
                'panda': { bg: '#98D8C8', glow: '#A8E8D8' },
                'rabbit': { bg: '#F7DC6F', glow: '#F9E67F' },
                'tiger': { bg: '#BB8FCE', glow: '#CB9FDE' }
            },
            special: {
                'rocket': { bg: '#FF4500', glow: '#FF6347' },
                'bomb': { bg: '#8B0000', glow: '#DC143C' }
            }
        };
        
        console.log('GamePageRenderer初始化完成');
    }

    // 初始化可爱风背景
    initCuteBackground() {
        try {
            console.log('开始初始化可爱风背景...');
            console.log('CuteBackground 类型:', typeof CuteBackground);

            // 检查 CuteBackground 类是否可用
            if (typeof CuteBackground !== 'undefined') {
                // 创建游戏界面背景实例，使用自定义配置获取方法
                this.cuteBackground = new CuteBackground(this.canvas, this.ctx, this.uiConfig);

                // 重写 getBackgroundConfig 方法使其使用游戏界面配置
                this.cuteBackground.getBackgroundConfig = () => {
                    return this.uiConfig ? this.uiConfig.getGameBackgroundConfig() : null;
                };

                console.log('游戏界面可爱风背景初始化成功:', this.cuteBackground);
            } else {
                console.warn('CuteBackground 类不可用，使用默认背景');
                console.log('可用的全局对象:', Object.keys(window).filter(key => key.includes('Cute')));
            }
        } catch (error) {
            console.error('初始化可爱风背景时出错:', error);
        }
    }

    // 根据关卡初始化颜色主题
    initLevelColors() {
        const level = this.core.level || 1;

        switch (level) {
            case 1:
                // 第一关：轻松愉快的浅绿色/蓝色
                this.levelColors = {
                    primary: '#E8F5E8',    // 浅绿色
                    secondary: '#B8E6B8',  // 中绿色
                    accent: '#87CEEB',     // 天蓝色
                    gradient: ['#E8F5E8', '#B8E6B8', '#87CEEB', '#98FB98']
                };
                break;
            case 2:
                // 第二关：浅紫色
                this.levelColors = {
                    primary: '#F0E6FF',    // 浅紫色
                    secondary: '#DDA0DD',  // 中紫色
                    accent: '#9370DB',     // 深紫色
                    gradient: ['#F0E6FF', '#DDA0DD', '#9370DB', '#BA55D3']
                };
                break;
            case 3:
            default:
                // 第三关：桃红色
                this.levelColors = {
                    primary: '#FFE5F1',    // 浅桃红
                    secondary: '#FFB6C1',  // 中桃红
                    accent: '#FF69B4',     // 深桃红
                    gradient: ['#FFE5F1', '#FFB6C1', '#FF69B4', '#FF1493']
                };
                break;
        }

        console.log(`关卡${level}颜色主题已设置`);
    }
    
    // 主渲染方法
    render() {
        this.animationTime += 0.016; // 60fps

        // 更新可爱风背景动画
        if (this.cuteBackground) {
            this.cuteBackground.update();
        }

        // 清空画布
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 渲染背景
        this.renderBackground();
        
        // 渲染UI界面
        this.renderUI();
        
        // 渲染游戏网格
        this.renderGrid();

        // 渲染道具栏
        this.renderPropBar();

        // 渲染特效
        this.renderEffects();

        // 渲染游戏状态
        this.renderGameStatus();

        // 渲染拖拽中的方块（最高优先级，显示在最前面）
        this.renderDraggingBlock();

        // 渲染炸弹卡拖拽
        if (this.core.events && this.core.events.isDraggingBomb) {
            this.renderBombDrag();
        }

        // 渲染通关成功界面
        if (this.core.showLevelCompleteUI) {
            this.renderLevelCompleteScreen();
        }
    }
    
    // 渲染可爱风背景
    renderBackground() {
        // 延迟初始化背景
        if (!this.backgroundInitialized) {
            this.initCuteBackground();
            this.backgroundInitialized = true;
        }

        // 使用可爱风背景类渲染
        if (this.cuteBackground) {
            console.log('使用可爱风背景渲染');
            this.cuteBackground.render();
        } else {
            console.log('cuteBackground 不存在，使用默认背景');
            // 后备方案：使用默认背景
            this.renderDefaultBackground();
        }
    }

    // 渲染可爱风背景
    renderCuteBackground(bgConfig) {
        this.ctx.save();

        // 渐变背景
        let gradient;
        if (bgConfig.gradient.type === 'radial') {
            gradient = this.ctx.createRadialGradient(
                this.canvas.width / 2, this.canvas.height / 2, 0,
                this.canvas.width / 2, this.canvas.height / 2,
                Math.max(this.canvas.width, this.canvas.height) / 2
            );
        } else {
            gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        }

        // 添加渐变色
        const colors = bgConfig.gradient.colors;
        for (let i = 0; i < colors.length; i++) {
            gradient.addColorStop(i / (colors.length - 1), colors[i]);
        }

        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // 渲染装饰元素
        this.renderBackgroundDecorations(bgConfig.decorations);

        this.ctx.restore();
    }

    // 渲染默认背景（后备方案）
    renderDefaultBackground() {
        const gradient = this.ctx.createRadialGradient(
            this.canvas.width / 2, this.canvas.height / 2, 0,
            this.canvas.width / 2, this.canvas.height / 2,
            Math.max(this.canvas.width, this.canvas.height) / 2
        );

        gradient.addColorStop(0, '#FFE4E6');
        gradient.addColorStop(0.5, '#FFF0F5');
        gradient.addColorStop(1, '#E6E6FA');

        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // 添加静态装饰
        this.renderStaticDecorations();
    }

    // 渲染背景装饰元素
    renderBackgroundDecorations(decorations) {
        if (!decorations) return;

        try {
            // 渲染小爱心
            if (decorations.hearts && decorations.hearts.enabled) {
                if (typeof this.renderHearts === 'function') {
                    this.renderHearts(decorations.hearts);
                } else {
                    console.warn('renderHearts 方法不存在');
                }
            }

            // 渲染小星星
            if (decorations.sparkles && decorations.sparkles.enabled) {
                if (typeof this.renderBackgroundSparkles === 'function') {
                    this.renderBackgroundSparkles(decorations.sparkles);
                } else {
                    console.warn('renderBackgroundSparkles 方法不存在');
                }
            }

            // 渲染泡泡
            if (decorations.bubbles && decorations.bubbles.enabled) {
                if (typeof this.renderBubbles === 'function') {
                    this.renderBubbles(decorations.bubbles);
                } else {
                    console.warn('renderBubbles 方法不存在');
                }
            }
        } catch (error) {
            console.error('渲染背景装饰元素时出错:', error);
        }
    }

    // 渲染小爱心
    renderHearts(heartsConfig) {
        this.ctx.save();
        this.ctx.globalAlpha = heartsConfig.opacity;

        for (let i = 0; i < heartsConfig.count; i++) {
            const x = Math.random() * this.canvas.width;
            const y = Math.random() * this.canvas.height;
            const size = heartsConfig.minSize + Math.random() * (heartsConfig.maxSize - heartsConfig.minSize);
            const color = heartsConfig.colors[Math.floor(Math.random() * heartsConfig.colors.length)];

            this.ctx.fillStyle = color;
            this.drawHeart(x, y, size);
        }

        this.ctx.restore();
    }

    // 绘制爱心形状
    drawHeart(x, y, size) {
        this.ctx.save();
        this.ctx.translate(x, y);
        this.ctx.scale(size / 10, size / 10);

        this.ctx.beginPath();
        this.ctx.moveTo(0, 3);
        this.ctx.bezierCurveTo(-5, -2, -10, 1, -5, 8);
        this.ctx.bezierCurveTo(0, 12, 0, 12, 0, 12);
        this.ctx.bezierCurveTo(0, 12, 0, 12, 5, 8);
        this.ctx.bezierCurveTo(10, 1, 5, -2, 0, 3);
        this.ctx.fill();

        this.ctx.restore();
    }

    // 渲染背景小星星
    renderBackgroundSparkles(sparklesConfig) {
        this.ctx.save();
        this.ctx.globalAlpha = sparklesConfig.opacity;

        for (let i = 0; i < sparklesConfig.count; i++) {
            const x = Math.random() * this.canvas.width;
            const y = Math.random() * this.canvas.height;
            const size = sparklesConfig.minSize + Math.random() * (sparklesConfig.maxSize - sparklesConfig.minSize);
            const color = sparklesConfig.colors[Math.floor(Math.random() * sparklesConfig.colors.length)];

            this.ctx.fillStyle = color;
            this.ctx.shadowColor = color;
            this.ctx.shadowBlur = size;

            this.drawStar(x, y, size);
        }

        this.ctx.restore();
    }

    // 绘制星星形状
    drawStar(x, y, size) {
        this.ctx.save();
        this.ctx.translate(x, y);

        this.ctx.beginPath();
        for (let i = 0; i < 5; i++) {
            const angle = (i * 4 * Math.PI) / 5;
            const x1 = Math.cos(angle) * size;
            const y1 = Math.sin(angle) * size;

            if (i === 0) {
                this.ctx.moveTo(x1, y1);
            } else {
                this.ctx.lineTo(x1, y1);
            }
        }
        this.ctx.closePath();
        this.ctx.fill();

        this.ctx.restore();
    }

    // 渲染泡泡
    renderBubbles(bubblesConfig) {
        this.ctx.save();
        this.ctx.globalAlpha = bubblesConfig.opacity;

        for (let i = 0; i < bubblesConfig.count; i++) {
            const x = Math.random() * this.canvas.width;
            const y = Math.random() * this.canvas.height;
            const size = bubblesConfig.minSize + Math.random() * (bubblesConfig.maxSize - bubblesConfig.minSize);
            const color = bubblesConfig.colors[Math.floor(Math.random() * bubblesConfig.colors.length)];

            this.ctx.fillStyle = color;
            this.ctx.beginPath();
            this.ctx.arc(x, y, size, 0, Math.PI * 2);
            this.ctx.fill();

            // 添加高光效果
            this.ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
            this.ctx.beginPath();
            this.ctx.arc(x - size * 0.3, y - size * 0.3, size * 0.3, 0, Math.PI * 2);
            this.ctx.fill();
        }

        this.ctx.restore();
    }

    // 渲染静态装饰
    renderStaticDecorations() {
        // 添加一些静态的几何装饰
        for (let i = 0; i < 6; i++) {
            const x = (this.canvas.width / 6) * i + 50;
            const y = 40;
            
            this.ctx.save();
            this.ctx.globalAlpha = 0.3;
            
            // 绘制小圆点装饰
            this.ctx.fillStyle = '#FFFFFF';
            this.ctx.beginPath();
            this.ctx.arc(x, y, 3, 0, Math.PI * 2);
            this.ctx.fill();
            
            // 绘制小星形装饰
            if (i % 2 === 0) {
                this.ctx.fillStyle = '#FFD700';
                this.ctx.translate(x, y + 15);
                this.ctx.beginPath();
                for (let j = 0; j < 5; j++) {
                    const angle = (j * Math.PI * 2) / 5;
                    const radius = j % 2 === 0 ? 6 : 3;
                    const px = Math.cos(angle) * radius;
                    const py = Math.sin(angle) * radius;
                    if (j === 0) {
                        this.ctx.moveTo(px, py);
                    } else {
                        this.ctx.lineTo(px, py);
                    }
                }
                this.ctx.closePath();
                this.ctx.fill();
            }
            
            this.ctx.restore();
        }
    }
    
    // 渲染UI界面 - 使用配置类管理布局
    renderUI() {
        // 获取统计栏配置
        const statsConfig = this.uiConfig.getStatsBarConfig();

        // 顶部信息栏背景 - 使用配置参数
        const headerGradient = this.ctx.createLinearGradient(
            statsConfig.x, statsConfig.y,
            statsConfig.x, statsConfig.y + statsConfig.height
        );
        headerGradient.addColorStop(0, '#FFB6C1'); // 可爱粉色
        headerGradient.addColorStop(1, '#FFC0CB'); // 可爱粉色

        this.ctx.fillStyle = headerGradient;
        this.ctx.beginPath();
        this.ctx.roundRect(statsConfig.x, statsConfig.y, statsConfig.width, statsConfig.height, statsConfig.borderRadius);
        this.ctx.fill();

        // 添加装饰边框 - 使用配置的内边框参数
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.roundRect(
            statsConfig.innerBorder.x,
            statsConfig.innerBorder.y,
            statsConfig.innerBorder.width,
            statsConfig.innerBorder.height,
            statsConfig.innerBorder.borderRadius
        );
        this.ctx.stroke();

        // 关卡名称 - 使用配置参数
        this.ctx.fillStyle = '#FF1493';
        this.ctx.font = 'bold 28px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.strokeStyle = '#FFFFFF';
        this.ctx.lineWidth = 3;
        this.ctx.strokeText(this.core.levelName, this.canvas.width / 2, statsConfig.y + 30);
        this.ctx.fillText(this.core.levelName, this.canvas.width / 2, statsConfig.y + 30);

        // 分3个模块显示：分数、目标、进度 - 使用配置参数
        const moduleWidth = (statsConfig.width - 60) / 3;
        const moduleY = statsConfig.y + 75;

        // 分数模块 - 使用全局变量
        this.ctx.fillStyle = '#4169E1';
        this.ctx.font = 'bold 16px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.strokeStyle = '#FFFFFF';
        this.ctx.lineWidth = 2;
        this.ctx.strokeText(`分数`, statsConfig.x + 30 + moduleWidth * 0.5, moduleY - 15);
        this.ctx.fillText(`分数`, statsConfig.x + 30 + moduleWidth * 0.5, moduleY - 15);
        this.ctx.strokeText(`${window.gameScore || 0}`, statsConfig.x + 30 + moduleWidth * 0.5, moduleY + 10);
        this.ctx.fillText(`${window.gameScore || 0}`, statsConfig.x + 30 + moduleWidth * 0.5, moduleY + 10);

        // 目标模块 - 使用全局变量
        this.ctx.fillStyle = '#32CD32';
        this.ctx.strokeText(`目标`, statsConfig.x + 30 + moduleWidth * 1.5, moduleY - 15);
        this.ctx.fillText(`目标`, statsConfig.x + 30 + moduleWidth * 1.5, moduleY - 15);
        this.ctx.strokeText(`${window.gameTargetScore || 0}`, statsConfig.x + 30 + moduleWidth * 1.5, moduleY + 10);
        this.ctx.fillText(`${window.gameTargetScore || 0}`, statsConfig.x + 30 + moduleWidth * 1.5, moduleY + 10);

        // 进度模块 - 使用全局变量
        const progressPercent = Math.round((window.gameProgress || 0) * 100);
        this.ctx.fillStyle = '#FF69B4';
        this.ctx.strokeText(`进度`, statsConfig.x + 30 + moduleWidth * 2.5, moduleY - 15);
        this.ctx.fillText(`进度`, statsConfig.x + 30 + moduleWidth * 2.5, moduleY - 15);
        this.ctx.strokeText(`${progressPercent}%`, statsConfig.x + 30 + moduleWidth * 2.5, moduleY + 10);
        this.ctx.fillText(`${progressPercent}%`, statsConfig.x + 30 + moduleWidth * 2.5, moduleY + 10);

        // 添加统计栏模块间的白色分割线（圆角处理）
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
        this.ctx.lineWidth = 2;
        this.ctx.lineCap = 'round'; // 圆角端点

        // 第一条分割线（分数和目标之间）
        const divider1X = statsConfig.x + 30 + moduleWidth;
        this.ctx.beginPath();
        this.ctx.moveTo(divider1X, moduleY - 25);
        this.ctx.lineTo(divider1X, moduleY + 20);
        this.ctx.stroke();

        // 第二条分割线（目标和进度之间）
        const divider2X = statsConfig.x + 30 + moduleWidth * 2;
        this.ctx.beginPath();
        this.ctx.moveTo(divider2X, moduleY - 25);
        this.ctx.lineTo(divider2X, moduleY + 20);
        this.ctx.stroke();

        // 返回按钮 - 使用配置参数
        const backButtonConfig = this.uiConfig.getBackButtonConfig();
        this.renderBackButton(
            backButtonConfig.x,
            backButtonConfig.y,
            backButtonConfig.width,
            backButtonConfig.height,
            backButtonConfig.text
        );
    }

    // 移除了进度条渲染方法

    // 渲染道具栏 - 使用配置类管理布局
    renderPropBar() {
        // 获取道具栏配置
        const propBarConfig = this.uiConfig.getPropBarConfig();

        // 改进的渐变背景 - 更美观的颜色
        const propGradient = this.ctx.createLinearGradient(
            propBarConfig.x, propBarConfig.y - 15,
            propBarConfig.x, propBarConfig.y + propBarConfig.height - 15
        );
        propGradient.addColorStop(0, '#FFE4E1'); // 可爱粉色
        propGradient.addColorStop(0.5, '#FFF0F5'); // 薰衣草腮红
        propGradient.addColorStop(1, '#F0F8FF'); // 爱丽丝蓝

        this.ctx.fillStyle = propGradient;
        this.ctx.beginPath();
        this.ctx.roundRect(
            propBarConfig.x, propBarConfig.y - 15,
            propBarConfig.width, propBarConfig.height,
            propBarConfig.borderRadius
        );
        this.ctx.fill();

        // 美化的边框 - 可爱风颜色
        this.ctx.strokeStyle = '#FFB6C1'; // 浅粉色边框
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.roundRect(
            propBarConfig.x, propBarConfig.y - 15,
            propBarConfig.width, propBarConfig.height,
            propBarConfig.borderRadius
        );
        this.ctx.stroke();

        // 内层装饰边框 - 使用配置参数
        this.ctx.strokeStyle = 'rgba(255, 215, 0, 0.7)'; // 金色
        this.ctx.lineWidth = 1;
        this.ctx.beginPath();
        this.ctx.roundRect(
            propBarConfig.x + 3, propBarConfig.y - 12,
            propBarConfig.width - 6, propBarConfig.height - 6,
            propBarConfig.innerBorderRadius
        );
        this.ctx.stroke();

        // 道具配置 - 更美观的颜色
        const props = [
            { type: 'refresh', name: '刷新卡', count: this.core.props.refresh, color: '#32CD32' }, // 酸橙绿
            { type: 'bomb', name: '炸弹卡', count: this.core.props.bomb, color: '#FF6347' }, // 番茄红
            { type: 'clear', name: '清屏卡', count: this.core.props.clear, color: '#1E90FF' } // 道奇蓝
        ];

        props.forEach((prop, index) => {
            // 使用配置参数计算道具位置
            const x = propBarConfig.propStartX + index * propBarConfig.propSpacing +
                     (propBarConfig.propSpacing - propBarConfig.propSize) / 2;
            const y = propBarConfig.y;

            // 道具按钮背景 - 圆角处理
            this.ctx.save();
            this.ctx.shadowColor = 'rgba(0, 0, 0, 0.4)';
            this.ctx.shadowBlur = 8;
            this.ctx.shadowOffsetX = 3;
            this.ctx.shadowOffsetY = 3;

            // 根据数量决定按钮状态
            const isAvailable = prop.count > 0;

            // 渐变背景 - 数量为0时使用灰色
            const buttonGradient = this.ctx.createLinearGradient(x, y, x, y + propBarConfig.propSize);
            if (isAvailable) {
                buttonGradient.addColorStop(0, prop.color);
                buttonGradient.addColorStop(1, this.darkenColor(prop.color, 0.3));
            } else {
                // 灰色渐变
                buttonGradient.addColorStop(0, '#808080');
                buttonGradient.addColorStop(1, '#606060');
            }

            this.ctx.fillStyle = buttonGradient;
            this.ctx.beginPath();
            this.ctx.roundRect(x, y, propBarConfig.propSize, propBarConfig.propSize, 8);
            this.ctx.fill();

            // 边框 - 数量为0时使用灰色边框
            this.ctx.strokeStyle = isAvailable ? '#FFFFFF' : '#999999';
            this.ctx.lineWidth = 2;
            this.ctx.beginPath();
            this.ctx.roundRect(x, y, propBarConfig.propSize, propBarConfig.propSize, 8);
            this.ctx.stroke();

            // 数量为0时添加禁用覆盖层
            if (!isAvailable) {
                this.ctx.fillStyle = 'rgba(0, 0, 0, 0.4)';
                this.ctx.beginPath();
                this.ctx.roundRect(x, y, propBarConfig.propSize, propBarConfig.propSize, 8);
                this.ctx.fill();
            }

            this.ctx.restore();

            // 道具图标（使用图片）
            const propImage = this.core.propImages[prop.type];
            if (propImage && propImage.complete) {
                const imageSize = propBarConfig.propSize * 0.7;

                // 数量为0时应用灰度滤镜
                if (!isAvailable) {
                    this.ctx.save();
                    this.ctx.filter = 'grayscale(100%) brightness(0.6)';
                }

                this.ctx.drawImage(
                    propImage,
                    x + (propBarConfig.propSize - imageSize) / 2,
                    y + (propBarConfig.propSize - imageSize) / 2,
                    imageSize,
                    imageSize
                );

                if (!isAvailable) {
                    this.ctx.restore();
                }
            } else {
                // 图片未加载时的备用方案
                this.ctx.fillStyle = isAvailable ? '#FFFFFF' : '#CCCCCC';
                this.ctx.font = 'bold 12px Arial, "Microsoft YaHei"';
                this.ctx.textAlign = 'center';
                this.ctx.textBaseline = 'middle';
                this.ctx.fillText(prop.name, x + propBarConfig.propSize/2, y + propBarConfig.propSize/2);
            }

            // 道具数量（显示在右上角）
            this.ctx.font = 'bold 16px Arial, "Microsoft YaHei"';
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';

            // 数量背景圆圈
            this.ctx.beginPath();
            this.ctx.arc(x + propBarConfig.propSize - 10, y + 10, 12, 0, Math.PI * 2);
            this.ctx.fillStyle = isAvailable ? '#FFFFFF' : '#CCCCCC';
            this.ctx.fill();
            this.ctx.strokeStyle = isAvailable ? '#FF0000' : '#999999';
            this.ctx.lineWidth = 2;
            this.ctx.stroke();

            // 数量文字
            this.ctx.fillStyle = isAvailable ? '#FF0000' : '#666666';
            this.ctx.fillText(prop.count.toString(), x + propBarConfig.propSize - 10, y + 10);

            // 激活状态指示
            if (this.core.propUsing.isActive && this.core.propUsing.type === prop.type) {
                this.ctx.strokeStyle = '#FFD700';
                this.ctx.lineWidth = 4;
                this.ctx.strokeRect(x - 2, y - 2, propBarConfig.propSize + 4, propBarConfig.propSize + 4);

                // 添加闪烁效果
                this.ctx.save();
                this.ctx.globalAlpha = 0.5 + 0.5 * Math.sin(this.animationTime * 5);
                this.ctx.fillStyle = '#FFD700';
                this.ctx.fillRect(x, y, propBarConfig.propSize, propBarConfig.propSize);
                this.ctx.restore();
            }
        });

        // 道具使用提示
        if (this.core.propUsing.isActive) {
            this.ctx.fillStyle = '#FF69B4';
            this.ctx.font = 'bold 18px Arial, "Microsoft YaHei"';
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';

            let tipText = '';
            if (this.core.propUsing.type === 'bomb') {
                tipText = '请点击要爆炸的位置';
            }

            if (tipText) {
                this.ctx.strokeStyle = '#FFFFFF';
                this.ctx.lineWidth = 2;
                this.ctx.strokeText(tipText, this.canvas.width / 2, propBarConfig.y + propBarConfig.propSize + 30);
                this.ctx.fillText(tipText, this.canvas.width / 2, propBarConfig.y + propBarConfig.propSize + 30);
            }
        }
    }
    
    // 渲染按钮
    renderButton(x, y, width, height, text, color) {
        this.ctx.save();
        
        // 按钮阴影
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        this.ctx.shadowBlur = 8;
        this.ctx.shadowOffsetX = 3;
        this.ctx.shadowOffsetY = 3;
        
        // 按钮背景渐变
        const buttonGradient = this.ctx.createLinearGradient(x, y, x, y + height);
        buttonGradient.addColorStop(0, color);
        buttonGradient.addColorStop(0.5, this.lightenColor(color, 20));
        buttonGradient.addColorStop(1, this.darkenColor(color, 10));
        
        this.ctx.fillStyle = buttonGradient;
        this.ctx.beginPath();
        this.ctx.roundRect(x, y, width, height, 15);
        this.ctx.fill();
        
        // 按钮边框
        this.ctx.shadowColor = 'transparent';
        this.ctx.strokeStyle = '#FFFFFF';
        this.ctx.lineWidth = 2;
        this.ctx.stroke();
        
        // 按钮文字
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = 'bold 16px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText(text, x + width / 2, y + height / 2);
        
        this.ctx.restore();
    }
    
    // 渲染返回按钮（半透明白色背景，黑色字体）
    renderBackButton(x, y, width, height, text) {
        this.ctx.save();
        
        // 按钮阴影
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
        this.ctx.shadowBlur = 6;
        this.ctx.shadowOffsetX = 2;
        this.ctx.shadowOffsetY = 2;
        
        // 半透明白色背景（透明度0.6）
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
        this.ctx.beginPath();
        this.ctx.roundRect(x, y, width, height, 15);
        this.ctx.fill();
        
        // 按钮边框
        this.ctx.shadowColor = 'transparent';
        this.ctx.strokeStyle = 'rgba(0, 0, 0, 0.3)';
        this.ctx.lineWidth = 1;
        this.ctx.stroke();
        
        // 黑色文字（确保完全居中）
        this.ctx.fillStyle = '#333333';
        this.ctx.font = 'bold 16px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        
        // 精确计算文本位置以确保完全居中
        const textX = Math.round(x + width / 2);
        const textY = Math.round(y + height / 2);
        this.ctx.fillText(text, textX, textY);
        
        this.ctx.restore();
    }
    

    
    // 渲染游戏网格（支持动态计算）
    renderGrid() {
        if (!this.core.grid) return;

        // 获取网格配置
        const gridConfig = this.core.uiConfig ? this.core.uiConfig.getGridConfig() : null;

        if (gridConfig) {
            // 使用动态配置
            this.renderGridBackground(gridConfig.startX, gridConfig.startY, gridConfig);

            // 渲染网格中的方块
            for (let row = 0; row < gridConfig.rows; row++) {
                for (let col = 0; col < gridConfig.cols; col++) {
                    const block = this.core.grid[row] && this.core.grid[row][col];
                    if (block && !block.isFalling) {
                        // 计算方块位置（考虑间距）
                        let x = gridConfig.startX + col * (gridConfig.blockSize + gridConfig.padding);
                        let y = gridConfig.startY + row * (gridConfig.blockSize + gridConfig.padding);

                        // 处理拖拽效果
                        if (this.core.events && this.core.events.isDragging) {
                            const dragStartRow = this.core.events.dragStartRow;
                            const dragStartCol = this.core.events.dragStartCol;

                            // 如果是被拖拽的方块，使用拖拽位置
                            if (row === dragStartRow && col === dragStartCol) {
                                const dragCurrentX = this.core.events.dragCurrentX;
                                const dragCurrentY = this.core.events.dragCurrentY;

                                if (dragCurrentX >= 0 && dragCurrentY >= 0) {
                                    x = dragCurrentX - gridConfig.blockSize / 2;
                                    y = dragCurrentY - gridConfig.blockSize / 2;

                                    // 添加拖拽时的视觉效果
                                    block.isDragging = true;
                                    block.dragAlpha = 0.8; // 半透明效果
                                    block.dragScale = 1.1; // 稍微放大
                                }
                            }
                        } else {
                            // 重置拖拽状态
                            block.isDragging = false;
                            block.dragAlpha = 1.0;
                            block.dragScale = 1.0;
                        }

                        this.renderBlockWithSize(block, x, y, gridConfig.blockSize);
                    }
                }
            }
        } else {
            // 后备方案：使用原来的方式
            const startX = this.core.gridStartX;
            const startY = this.core.gridStartY;

            this.renderGridBackground(startX, startY);

            for (let row = 0; row < this.core.gridSizeY; row++) {
                for (let col = 0; col < this.core.gridSizeX; col++) {
                    const block = this.core.grid[row][col];
                    // 排除拖拽中的方块，它们将在最后单独渲染
                    if (block && !block.isFalling && !this.isBlockBeingDragged(row, col)) {
                        const x = startX + col * this.core.blockSize;
                        const y = startY + row * this.core.blockSize;
                        this.renderBlock(block, x, y);
                    }
                }
            }
        }

        // 渲染掉落中的方块
        if (this.core.animator && this.core.animator.falling) {
            this.core.animator.render(this);
        }
    }
    
    // 渲染网格背景（支持动态配置）
    renderGridBackground(startX, startY, gridConfig = null) {
        let gridWidth, gridHeight, cols, rows, blockSize, padding;

        if (gridConfig) {
            // 使用动态配置
            gridWidth = gridConfig.gridWidth;
            gridHeight = gridConfig.gridHeight;
            cols = gridConfig.cols;
            rows = gridConfig.rows;
            blockSize = gridConfig.blockSize;
            padding = gridConfig.padding;
        } else {
            // 后备方案
            gridWidth = this.core.gridSizeX * this.core.blockSize;
            gridHeight = this.core.gridSizeY * this.core.blockSize;
            cols = this.core.gridSizeX;
            rows = this.core.gridSizeY;
            blockSize = this.core.blockSize;
            padding = 0;
        }
        
        // 获取可爱风网格配置
        const cuteGridConfig = this.uiConfig ? this.uiConfig.getGameGridConfig() : null;

        // 美化的网格背景
        this.ctx.save();

        // 外层发光阴影
        this.ctx.shadowColor = cuteGridConfig && cuteGridConfig.background.shadow ?
            cuteGridConfig.background.shadow.color : 'rgba(255, 182, 193, 0.4)';
        this.ctx.shadowBlur = cuteGridConfig && cuteGridConfig.background.shadow ?
            cuteGridConfig.background.shadow.blur : 15;
        this.ctx.shadowOffsetX = 0;
        this.ctx.shadowOffsetY = cuteGridConfig && cuteGridConfig.background.shadow ?
            cuteGridConfig.background.shadow.offsetY : 8;

        // 主背景
        this.ctx.fillStyle = cuteGridConfig && cuteGridConfig.background ?
            cuteGridConfig.background.color : 'rgba(255, 255, 255, 0.15)';
        this.ctx.beginPath();
        this.ctx.roundRect(startX - 10, startY - 10, gridWidth + 20, gridHeight + 20, 15);
        this.ctx.fill();

        // 重置阴影
        this.ctx.shadowColor = 'transparent';

        // 内层高光效果
        const highlightGradient = this.ctx.createLinearGradient(
            startX - 10, startY - 10,
            startX - 10, startY - 10 + (gridHeight + 20) * 0.3
        );
        highlightGradient.addColorStop(0, 'rgba(255, 255, 255, 0.4)');
        highlightGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

        this.ctx.fillStyle = highlightGradient;
        this.ctx.beginPath();
        this.ctx.roundRect(startX - 8, startY - 8, gridWidth + 16, (gridHeight + 16) * 0.3, 13);
        this.ctx.fill();

        // 装饰边框
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.6)';
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.roundRect(startX - 9, startY - 9, gridWidth + 18, gridHeight + 18, 14);
        this.ctx.stroke();

        // 内边框
        this.ctx.strokeStyle = 'rgba(255, 182, 193, 0.3)';
        this.ctx.lineWidth = 1;
        this.ctx.beginPath();
        this.ctx.roundRect(startX - 5, startY - 5, gridWidth + 10, gridHeight + 10, 10);
        this.ctx.stroke();

        this.ctx.restore();

        // 渲染格子背景（增加间隔感）
        this.renderGridCells(startX, startY, cols, rows, blockSize, padding, cuteGridConfig);

        // 网格线（可选，现在可以去掉因为格子间隔已经提供了分隔效果）
        // this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
        // this.ctx.lineWidth = 1;
    }

    // 渲染网格格子背景
    renderGridCells(startX, startY, cols, rows, blockSize, spacing, cuteGridConfig) {
        if (!cuteGridConfig || !cuteGridConfig.cell) {
            return; // 如果没有格子配置，不渲染格子背景
        }

        const cellConfig = cuteGridConfig.cell;
        const inset = cellConfig.inset || 0; // 格子内缩像素
        const actualSpacing = spacing || 0;

        this.ctx.save();

        for (let row = 0; row < rows; row++) {
            for (let col = 0; col < cols; col++) {
                // 计算格子位置（考虑间距）
                const cellX = startX + col * (blockSize + actualSpacing) + inset;
                const cellY = startY + row * (blockSize + actualSpacing) + inset;
                const cellSize = blockSize - inset * 2; // 格子大小减去内缩

                // 绘制格子背景
                this.ctx.fillStyle = cellConfig.backgroundColor;
                this.ctx.beginPath();
                this.ctx.roundRect(cellX, cellY, cellSize, cellSize, cellConfig.borderRadius);
                this.ctx.fill();

                // 绘制格子边框（如果需要）
                if (cellConfig.border && cellConfig.border.width > 0) {
                    this.ctx.strokeStyle = cellConfig.border.color;
                    this.ctx.lineWidth = cellConfig.border.width;
                    this.ctx.stroke();
                }
            }
        }

        this.ctx.restore();
    }
    
    // 渲染方块（静态版本，无浮动动画）
    renderBlock(block, x, y) {
        this.ctx.save();
        
        // 应用透明度和缩放（仅选中状态有轻微缩放）
        this.ctx.globalAlpha = block.alpha;
        
        let scaleOffset = block.scale;
        if (block.isSelected) {
            scaleOffset = 1.1 + Math.sin(this.animationTime * 10) * 0.05; // 更明显的缩放和脉动
        }
        
        this.ctx.translate(x + this.core.blockSize / 2, y + this.core.blockSize / 2);
        this.ctx.scale(scaleOffset, scaleOffset);
        this.ctx.rotate(block.rotation);
        
        // 方块阴影
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
        this.ctx.shadowBlur = 6;
        this.ctx.shadowOffsetX = 2;
        this.ctx.shadowOffsetY = 2;
        
        // 方块背景渐变 - 支持特殊方块
        let blockColor;
        if (block.blockType === 'special') {
            blockColor = this.colors.special[block.type] || this.colors.special['rocket'];
        } else {
            blockColor = this.colors.animals[block.type] || this.colors.animals['cat'];
        }

        const blockGradient = this.ctx.createRadialGradient(0, 0, 0, 0, 0, this.core.blockSize / 2);
        blockGradient.addColorStop(0, blockColor.glow);
        blockGradient.addColorStop(0.7, blockColor.bg);
        blockGradient.addColorStop(1, this.darkenColor(blockColor.bg, 15));
        
        this.ctx.fillStyle = blockGradient;
        this.ctx.beginPath();
        this.ctx.roundRect(-this.core.blockSize / 2, -this.core.blockSize / 2, 
                         this.core.blockSize, this.core.blockSize, 8);
        this.ctx.fill();
        
        // 方块边框
        this.ctx.shadowColor = 'transparent';
        if (block.isSelected) {
            // 选中状态：金色发光边框
            this.ctx.strokeStyle = '#FFD700';
            this.ctx.lineWidth = 4;
            this.ctx.shadowColor = '#FFD700';
            this.ctx.shadowBlur = 8;
            this.ctx.stroke();

            // 添加内层白色边框
            this.ctx.shadowColor = 'transparent';
            this.ctx.strokeStyle = '#FFFFFF';
            this.ctx.lineWidth = 2;
            this.ctx.stroke();
        } else {
            this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.6)';
            this.ctx.lineWidth = 2;
            this.ctx.stroke();
        }
        
        // 内部高光
        const highlightGradient = this.ctx.createLinearGradient(
            -this.core.blockSize / 2, -this.core.blockSize / 2,
            this.core.blockSize / 4, this.core.blockSize / 4
        );
        highlightGradient.addColorStop(0, 'rgba(255, 255, 255, 0.3)');
        highlightGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
        
        this.ctx.fillStyle = highlightGradient;
        this.ctx.beginPath();
        this.ctx.roundRect(-this.core.blockSize / 2, -this.core.blockSize / 2, 
                         this.core.blockSize, this.core.blockSize, 8);
        this.ctx.fill();
        
        // 方块内容渲染
        this.ctx.shadowColor = 'transparent';

        if (block.blockType === 'special') {
            // 渲染特殊方块图标
            this.renderSpecialBlockIcon(block);
        } else {
            // 渲染普通萌宠
            const animalImage = this.core.animalImages[block.type];
            if (animalImage && animalImage.complete) {
                // 绘制萌宠图片
                const imageSize = this.core.blockSize * 0.7;
                this.ctx.drawImage(
                    animalImage,
                    -imageSize / 2,
                    -imageSize / 2,
                    imageSize,
                    imageSize
                );
            } else {
                // 图片未加载时的备用方案 - 显示文字
                this.ctx.fillStyle = '#FFFFFF';
                this.ctx.font = `bold ${this.core.blockSize * 0.3}px Arial, "Microsoft YaHei"`;
                this.ctx.textAlign = 'center';
                this.ctx.textBaseline = 'middle';

                // 显示萌宠名称的中文
                const animalNames = {
                    'cat': '猫',
                    'dog': '狗',
                    'elephant': '象',
                    'fox': '狐',
                    'frog': '蛙',
                    'monkey': '猴',
                    'panda': '熊',
                    'rabbit': '兔',
                    'tiger': '虎'
                };

                this.ctx.fillText(animalNames[block.type] || block.type, 0, 0);
            }
        }
        
        this.ctx.restore();
    }

    // 渲染方块（支持自定义大小）
    renderBlockWithSize(block, x, y, blockSize) {
        this.ctx.save();

        // 应用透明度和缩放
        let alpha = block.alpha;
        let scaleOffset = block.scale;
        let rotationOffset = block.rotation;

        // 处理拖拽效果
        if (block.isDragging) {
            alpha = block.dragAlpha || 0.8;
            scaleOffset = block.dragScale || 1.1;
        }

        // 处理刷新动画
        if (block.isRefreshing && block.refreshPhase) {
            const elapsed = Date.now() - block.refreshStartTime;
            const duration = 1000; // 1秒动画时长
            const progress = Math.min(elapsed / duration, 1);

            if (block.refreshPhase === 'shrinking') {
                // 缩小阶段：从1缩小到0.1，同时旋转
                scaleOffset = 1 - progress * 0.9;
                rotationOffset = progress * Math.PI * 2;
            } else if (block.refreshPhase === 'expanding') {
                // 放大阶段：从0.1放大到1，同时旋转回正
                scaleOffset = 0.1 + progress * 0.9;
                rotationOffset = (1 - progress) * Math.PI * 2;
            }
        } else if (block.isSelected) {
            scaleOffset = 1.1 + Math.sin(this.animationTime * 10) * 0.05;
        }

        this.ctx.globalAlpha = alpha;
        this.ctx.translate(x + blockSize / 2, y + blockSize / 2);
        this.ctx.scale(scaleOffset, scaleOffset);
        this.ctx.rotate(rotationOffset);

        // 方块阴影
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
        this.ctx.shadowBlur = 6;
        this.ctx.shadowOffsetX = 2;
        this.ctx.shadowOffsetY = 2;

        // 方块背景渐变 - 与原版保持一致
        let blockColorConfig;
        if (block.blockType === 'special') {
            blockColorConfig = this.colors.special[block.type] || this.colors.special['rocket'];
        } else {
            blockColorConfig = this.colors.animals[block.type] || this.colors.animals['cat'];
        }

        // 使用径向渐变，与原版保持一致
        const gradient = this.ctx.createRadialGradient(0, 0, 0, 0, 0, blockSize / 2);
        gradient.addColorStop(0, blockColorConfig.glow);
        gradient.addColorStop(0.7, blockColorConfig.bg);
        gradient.addColorStop(1, this.darkenColor(blockColorConfig.bg, 15));

        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.ctx.roundRect(-blockSize/2, -blockSize/2, blockSize, blockSize, blockSize * 0.15);
        this.ctx.fill();

        // 方块边框
        this.ctx.shadowColor = 'transparent';
        this.ctx.strokeStyle = this.darkenColor(blockColorConfig.bg, 0.5);
        this.ctx.lineWidth = 2;
        this.ctx.stroke();

        this.ctx.shadowColor = 'transparent';

        // 渲染内容
        if (block.blockType === 'special') {
            this.renderSpecialBlockIconWithSize(block, blockSize);
        } else {
            // 渲染萌宠图标或文字
            const animalImage = this.core.animalImages[block.type];
            if (animalImage && animalImage.complete) {
                const imageSize = blockSize * 0.7;
                this.ctx.drawImage(animalImage, -imageSize/2, -imageSize/2, imageSize, imageSize);
            } else {
                // 文字备用方案
                this.ctx.fillStyle = '#FFFFFF';
                this.ctx.font = `bold ${blockSize * 0.3}px Arial, "Microsoft YaHei"`;
                this.ctx.textAlign = 'center';
                this.ctx.textBaseline = 'middle';

                const animalNames = {
                    'cat': '猫', 'dog': '狗', 'elephant': '象', 'fox': '狐', 'frog': '蛙',
                    'monkey': '猴', 'panda': '熊', 'rabbit': '兔', 'tiger': '虎'
                };

                this.ctx.fillText(animalNames[block.type] || block.type, 0, 0);
            }
        }

        this.ctx.restore();
    }

    // 渲染特殊方块图标（支持自定义大小）
    renderSpecialBlockIconWithSize(block, blockSize) {
        if (block.type === 'rocket') {
            const rocketImage = this.core.propImages['rocket'];
            if (rocketImage && rocketImage.complete) {
                const imageSize = blockSize * 0.6;
                this.ctx.drawImage(rocketImage, -imageSize/2, -imageSize/2, imageSize, imageSize);
            } else {
                // 文字备用方案
                this.ctx.fillStyle = '#FFFFFF';
                this.ctx.font = `bold ${blockSize * 0.25}px Arial, "Microsoft YaHei"`;
                this.ctx.textAlign = 'center';
                this.ctx.textBaseline = 'middle';
                this.ctx.fillText('🚀', 0, 0);
            }
        } else if (block.type === 'bomb') {
            // 5连合成的小炸弹
            const bombImage = this.core.propImages['bomb'];
            if (bombImage && bombImage.complete) {
                const imageSize = blockSize * 0.6;
                this.ctx.drawImage(bombImage, -imageSize/2, -imageSize/2, imageSize, imageSize);
            } else {
                // 文字备用方案
                this.ctx.fillStyle = '#FFFFFF';
                this.ctx.font = `bold ${blockSize * 0.25}px Arial, "Microsoft YaHei"`;
                this.ctx.textAlign = 'center';
                this.ctx.textBaseline = 'middle';
                this.ctx.fillText('💣', 0, 0);
            }
        }
    }

    // 渲染特殊方块图标
    renderSpecialBlockIcon(block) {
        if (block.type === 'rocket') {
            // 尝试使用火箭图片
            const rocketImage = this.core.propImages['rocket'];
            if (rocketImage && rocketImage.complete) {
                const imageSize = this.core.blockSize * 0.7;
                this.ctx.drawImage(
                    rocketImage,
                    -imageSize / 2,
                    -imageSize / 2,
                    imageSize,
                    imageSize
                );
            } else {
                // 图片未加载时的备用方案
                this.ctx.fillStyle = '#FFFFFF';
                this.ctx.font = `bold ${this.core.blockSize * 0.3}px Arial, "Microsoft YaHei"`;
                this.ctx.textAlign = 'center';
                this.ctx.textBaseline = 'middle';
                this.ctx.fillText('火箭', 0, 0);
            }

            // 添加闪烁效果
            this.ctx.save();
            this.ctx.globalAlpha = 0.5 + 0.5 * Math.sin(this.animationTime * 6);
            this.ctx.fillStyle = '#FFD700';
            this.ctx.beginPath();
            this.ctx.arc(0, 0, this.core.blockSize * 0.4, 0, Math.PI * 2);
            this.ctx.fill();
            this.ctx.restore();

        } else if (block.type === 'bomb') {
            // 尝试使用炸弹图片（5连合成的小炸弹）
            const bombImage = this.core.propImages['bomb'];
            if (bombImage && bombImage.complete) {
                const imageSize = this.core.blockSize * 0.7;
                this.ctx.drawImage(
                    bombImage,
                    -imageSize / 2,
                    -imageSize / 2,
                    imageSize,
                    imageSize
                );
            } else {
                // 图片未加载时的备用方案
                this.ctx.fillStyle = '#FFFFFF';
                this.ctx.font = `bold ${this.core.blockSize * 0.3}px Arial, "Microsoft YaHei"`;
                this.ctx.textAlign = 'center';
                this.ctx.textBaseline = 'middle';
                this.ctx.fillText('炸弹', 0, 0);
            }

            // 添加危险闪烁效果
            this.ctx.save();
            this.ctx.globalAlpha = 0.3 + 0.3 * Math.sin(this.animationTime * 8);
            this.ctx.fillStyle = '#FF0000';
            this.ctx.beginPath();
            this.ctx.arc(0, 0, this.core.blockSize * 0.45, 0, Math.PI * 2);
            this.ctx.fill();
            this.ctx.restore();
        }
    }
    
    // 渲染特效
    renderEffects() {
        // 渲染粒子效果
        this.renderParticles();
        
        // 渲染闪烁效果
        this.renderSparkles();
        
        // 渲染浮动文字
        this.renderFloatingTexts();
    }
    
    // 渲染粒子效果
    renderParticles() {
        this.core.particles.forEach(particle => {
            this.ctx.save();
            this.ctx.globalAlpha = particle.life;
            this.ctx.fillStyle = particle.color;
            this.ctx.beginPath();
            this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            this.ctx.fill();
            this.ctx.restore();
        });
    }
    
    // 渲染闪烁效果
    renderSparkles() {
        this.core.sparkles.forEach(sparkle => {
            this.ctx.save();
            this.ctx.globalAlpha = sparkle.life * (0.5 + 0.5 * Math.sin(sparkle.twinkle));
            this.ctx.fillStyle = '#FFD700';
            this.ctx.translate(sparkle.x, sparkle.y);
            this.ctx.rotate(sparkle.twinkle);
            
            // 绘制星形
            this.ctx.beginPath();
            for (let i = 0; i < 4; i++) {
                const angle = (i * Math.PI) / 2;
                const x = Math.cos(angle) * sparkle.size;
                const y = Math.sin(angle) * sparkle.size;
                if (i === 0) {
                    this.ctx.moveTo(x, y);
                } else {
                    this.ctx.lineTo(x, y);
                }
            }
            this.ctx.closePath();
            this.ctx.fill();
            
            this.ctx.restore();
        });
    }
    
    // 渲染浮动文字
    renderFloatingTexts() {
        this.core.floatingTexts.forEach(text => {
            this.ctx.save();

            if (text.isCombo) {
                // 连击文字特殊效果
                this.renderComboText(text);
            } else {
                // 普通浮动文字 - 增大字体大小
                this.ctx.globalAlpha = text.life;
                this.ctx.fillStyle = text.color;
                this.ctx.font = `bold ${28 * text.scale}px Arial, "Microsoft YaHei"`; // 从20改为28
                this.ctx.textAlign = 'center';
                this.ctx.textBaseline = 'middle';

                // 文字描边效果
                this.ctx.strokeStyle = '#FFFFFF';
                this.ctx.lineWidth = 3; // 增加描边宽度
                this.ctx.strokeText(text.text, text.x, text.y);
                this.ctx.fillText(text.text, text.x, text.y);
            }

            this.ctx.restore();
        });
    }

    // 渲染连击文字特效
    renderComboText(text) {
        // 脉动效果
        text.pulsePhase += 0.2;
        const pulseScale = 1 + Math.sin(text.pulsePhase) * 0.2;
        const finalScale = text.scale * pulseScale;

        // 透明度
        this.ctx.globalAlpha = text.life;

        // 阴影效果
        this.ctx.shadowColor = text.color;
        this.ctx.shadowBlur = 20;
        this.ctx.shadowOffsetX = 0;
        this.ctx.shadowOffsetY = 0;

        // 文字样式
        this.ctx.fillStyle = text.color;
        this.ctx.font = `bold ${30 * finalScale}px Arial, "Microsoft YaHei"`;
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';

        // 多层描边效果
        this.ctx.strokeStyle = '#000000';
        this.ctx.lineWidth = 6;
        this.ctx.strokeText(text.text, text.x, text.y);

        this.ctx.strokeStyle = '#FFFFFF';
        this.ctx.lineWidth = 3;
        this.ctx.strokeText(text.text, text.x, text.y);

        // 填充文字
        this.ctx.fillText(text.text, text.x, text.y);

        // 清除阴影
        this.ctx.shadowColor = 'transparent';
    }
    
    // 渲染游戏状态
    renderGameStatus() {
        if (this.core.showExitDialog) {
            this.renderExitDialog();
        } else if (this.core.isLevelComplete) {
            this.renderVictoryScreen();
        } else if (this.core.isGameOver) {
            this.renderGameOverScreen();
        }
    }
    
    // 渲染退出确认弹框
    renderExitDialog() {
        // 移除半透明遮罩，直接渲染对话框
        
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;
        
        // 弹框背景
        const dialogWidth = 300;
        const dialogHeight = 180;
        const dialogX = centerX - dialogWidth / 2;
        const dialogY = centerY - dialogHeight / 2;
        
        // 弹框阴影
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        this.ctx.shadowBlur = 15;
        this.ctx.shadowOffsetX = 0;
        this.ctx.shadowOffsetY = 5;
        
        // 弹框背景渐变
        const dialogGradient = this.ctx.createLinearGradient(dialogX, dialogY, dialogX, dialogY + dialogHeight);
        dialogGradient.addColorStop(0, '#FFFFFF');
        dialogGradient.addColorStop(1, '#F8F9FA');
        
        this.ctx.fillStyle = dialogGradient;
        this.ctx.beginPath();
        this.ctx.roundRect(dialogX, dialogY, dialogWidth, dialogHeight, 15);
        this.ctx.fill();
        
        // 弹框边框
        this.ctx.shadowColor = 'transparent';
        this.ctx.strokeStyle = '#FF69B4';
        this.ctx.lineWidth = 3;
        this.ctx.stroke();
        
        // 标题
        this.ctx.fillStyle = '#FF1493';
        this.ctx.font = 'bold 24px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText('确认退出', centerX, centerY - 40);
        
        // 提示文字
        this.ctx.fillStyle = '#666666';
        this.ctx.font = '18px Arial, "Microsoft YaHei"';
        this.ctx.fillText('确定要退出当前游戏吗？', centerX, centerY - 10);
        this.ctx.fillText('游戏进度将会丢失', centerX, centerY + 10);
        
        // 继续游戏按钮
        this.renderButton(centerX - 120, centerY + 20, 100, 40, '继续游戏', '#4CAF50');
        
        // 返回主页按钮
        this.renderButton(centerX + 20, centerY + 20, 100, 40, '返回主页', '#FF6B6B');
    }
    
    // 渲染胜利界面
    renderVictoryScreen() {
        // 移除全屏遮罩，直接渲染内容
        
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;
        
        // 胜利文字
        this.ctx.fillStyle = '#FFD700';
        this.ctx.font = 'bold 48px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.strokeStyle = '#FFFFFF';
        this.ctx.lineWidth = 4;
        this.ctx.strokeText('恭喜过关！', centerX, centerY - 50);
        this.ctx.fillText('恭喜过关！', centerX, centerY - 50);
        
        // 分数统计
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = '24px Arial, "Microsoft YaHei"';
        this.ctx.fillText(`最终分数: ${this.core.score}`, centerX, centerY + 20);
        
        // 提示文字
        this.ctx.font = '18px Arial, "Microsoft YaHei"';
        this.ctx.fillStyle = '#CCCCCC';
        this.ctx.fillText('点击屏幕继续', centerX, centerY + 100);
    }
    
    // 渲染游戏结束界面
    renderGameOverScreen() {
        // 移除全屏遮罩，直接渲染内容
        
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;
        
        // 游戏结束文字
        this.ctx.fillStyle = '#FF6B6B';
        this.ctx.font = 'bold 42px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText('游戏结束', centerX, centerY - 50);
        
        // 分数统计
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = '20px Arial, "Microsoft YaHei"';
        this.ctx.fillText(`最终分数: ${this.core.score}`, centerX, centerY);
        
        // 提示文字
        this.ctx.font = '16px Arial, "Microsoft YaHei"';
        this.ctx.fillStyle = '#CCCCCC';
        this.ctx.fillText('点击屏幕重新开始', centerX, centerY + 50);
    }



    // 渲染炸弹卡拖拽
    renderBombDrag() {
        const events = this.core.events;
        if (!events || !events.isDraggingBomb) return;

        const x = events.bombDragCurrentX;
        const y = events.bombDragCurrentY;
        const size = 50;

        // 半透明炸弹图标
        this.ctx.save();
        this.ctx.globalAlpha = 0.8;

        // 炸弹背景
        this.ctx.fillStyle = '#F44336';
        this.ctx.fillRect(x - size/2, y - size/2, size, size);

        // 炸弹图片
        const bombImage = this.core.propImages['bomb'];
        if (bombImage && bombImage.complete) {
            const imageSize = size * 0.8;
            this.ctx.drawImage(
                bombImage,
                x - imageSize/2,
                y - imageSize/2,
                imageSize,
                imageSize
            );
        } else {
            // 备用文字
            this.ctx.fillStyle = '#FFFFFF';
            this.ctx.font = 'bold 12px Arial, "Microsoft YaHei"';
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';
            this.ctx.fillText('炸弹', x, y);
        }

        this.ctx.restore();

        // 绘制拖拽轨迹
        this.ctx.save();
        this.ctx.strokeStyle = 'rgba(244, 67, 54, 0.5)';
        this.ctx.lineWidth = 3;
        this.ctx.setLineDash([5, 5]);
        this.ctx.beginPath();
        this.ctx.moveTo(events.bombDragStartX, events.bombDragStartY);
        this.ctx.lineTo(x, y);
        this.ctx.stroke();
        this.ctx.restore();
    }
    
    // 获取按钮信息（供事件处理使用）
    getButtons() {
        // 使用配置类获取按钮信息
        const backButtonConfig = this.uiConfig.getBackButtonConfig();

        const buttons = {
            back: {
                id: 'back',
                x: backButtonConfig.x,
                y: backButtonConfig.y,
                width: backButtonConfig.width,
                height: backButtonConfig.height,
                text: backButtonConfig.text
            }
        };
        
        // 如果显示退出确认弹框，添加弹框按钮
        if (this.core.showExitDialog) {
            buttons.continueGame = {
                id: 'continueGame',
                x: this.canvas.width / 2 - 120,
                y: this.canvas.height / 2 + 20,
                width: 100,
                height: 40,
                text: '继续游戏'
            };
            buttons.exitGame = {
                id: 'exitGame',
                x: this.canvas.width / 2 + 20,
                y: this.canvas.height / 2 + 20,
                width: 100,
                height: 40,
                text: '返回主页'
            };
        }
        
        return buttons;
    }
    
    // 检查点击是否在按钮内
    isPointInButton(x, y, button) {
        return x >= button.x && 
               x <= button.x + button.width && 
               y >= button.y && 
               y <= button.y + button.height;
    }
    
    // 颜色工具函数
    lightenColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) + amt;
        const G = (num >> 8 & 0x00FF) + amt;
        const B = (num & 0x0000FF) + amt;
        return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
            (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
            (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }
    
    darkenColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) - amt;
        const G = (num >> 8 & 0x00FF) - amt;
        const B = (num & 0x0000FF) - amt;
        return "#" + (0x1000000 + (R > 255 ? 255 : R < 0 ? 0 : R) * 0x10000 +
            (G > 255 ? 255 : G < 0 ? 0 : G) * 0x100 +
            (B > 255 ? 255 : B < 0 ? 0 : B)).toString(16).slice(1);
    }

    // 检查方块是否正在被拖拽
    isBlockBeingDragged(row, col) {
        if (!this.core.events) return false;

        // 检查是否是拖拽起始位置的方块
        return this.core.events.isDragging &&
               this.core.events.dragStartRow === row &&
               this.core.events.dragStartCol === col;
    }

    // 渲染拖拽中的方块（最高优先级）
    renderDraggingBlock() {
        if (!this.core.events || !this.core.events.isDragging) {
            return;
        }

        const events = this.core.events;
        const dragStartRow = events.dragStartRow;
        const dragStartCol = events.dragStartCol;

        // 获取被拖拽的方块
        const draggedBlock = this.core.grid[dragStartRow] && this.core.grid[dragStartRow][dragStartCol];
        if (!draggedBlock) {
            return;
        }

        // 计算拖拽位置
        let renderX, renderY;

        if (events.dragCurrentX !== undefined && events.dragCurrentY !== undefined) {
            // 使用当前拖拽位置，让方块跟随手指
            renderX = events.dragCurrentX - this.core.blockSize / 2;
            renderY = events.dragCurrentY - this.core.blockSize / 2;
        } else {
            // 后备方案：使用原始位置
            renderX = this.core.gridStartX + dragStartCol * this.core.blockSize;
            renderY = this.core.gridStartY + dragStartRow * this.core.blockSize;
        }

        // 保存当前状态
        this.ctx.save();

        // 为拖拽方块添加特殊效果
        this.ctx.globalAlpha = 0.9; // 稍微透明
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
        this.ctx.shadowBlur = 15;
        this.ctx.shadowOffsetX = 5;
        this.ctx.shadowOffsetY = 5;

        // 创建一个临时的拖拽方块副本，添加特殊效果
        const draggedBlockCopy = { ...draggedBlock };
        draggedBlockCopy.scale = (draggedBlock.scale || 1) * 1.1; // 放大10%
        draggedBlockCopy.isSelected = true; // 强制显示选中效果

        // 渲染拖拽中的方块
        this.renderBlock(draggedBlockCopy, renderX, renderY);

        // 恢复状态
        this.ctx.restore();

        // 如果有拖拽目标位置，绘制目标位置的提示
        if (events.dragEndRow >= 0 && events.dragEndCol >= 0) {
            this.renderDragTarget(events.dragEndRow, events.dragEndCol);
        }
    }

    // 渲染拖拽目标位置的提示
    renderDragTarget(targetRow, targetCol) {
        const targetX = this.core.gridStartX + targetCol * this.core.blockSize;
        const targetY = this.core.gridStartY + targetRow * this.core.blockSize;

        this.ctx.save();

        // 绘制目标位置的高亮框
        this.ctx.strokeStyle = '#FFD700'; // 金色
        this.ctx.lineWidth = 3;
        this.ctx.globalAlpha = 0.8;

        // 添加发光效果
        this.ctx.shadowColor = '#FFD700';
        this.ctx.shadowBlur = 10;

        this.ctx.beginPath();
        this.ctx.roundRect(targetX, targetY, this.core.blockSize, this.core.blockSize, 8);
        this.ctx.stroke();

        // 绘制内部高亮
        this.ctx.fillStyle = 'rgba(255, 215, 0, 0.2)';
        this.ctx.shadowColor = 'transparent';
        this.ctx.fill();

        this.ctx.restore();
    }

    // 渲染通关成功界面
    renderLevelCompleteScreen() {
        // 半透明遮罩
        this.ctx.save();
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;

        // 通关成功文字
        this.ctx.fillStyle = '#FFD700';
        this.ctx.font = 'bold 48px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';

        // 添加发光效果
        this.ctx.shadowColor = '#FFD700';
        this.ctx.shadowBlur = 20;
        this.ctx.shadowOffsetX = 0;
        this.ctx.shadowOffsetY = 0;

        // 添加描边
        this.ctx.strokeStyle = '#FFFFFF';
        this.ctx.lineWidth = 4;
        this.ctx.strokeText('恭喜过关！', centerX, centerY - 50);
        this.ctx.fillText('恭喜过关！', centerX, centerY - 50);

        // 分数统计
        this.ctx.shadowBlur = 0;
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = '24px Arial, "Microsoft YaHei"';
        this.ctx.fillText(`最终分数: ${this.core.score}`, centerX, centerY + 20);
        this.ctx.fillText(`目标分数: ${this.core.targetScore}`, centerX, centerY + 50);

        // 提示文字
        this.ctx.font = '18px Arial, "Microsoft YaHei"';
        this.ctx.fillStyle = '#CCCCCC';
        this.ctx.fillText('点击屏幕继续', centerX, centerY + 100);

        this.ctx.restore();
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GamePageRenderer;
} else {
    window.GamePageRenderer = GamePageRenderer;
}