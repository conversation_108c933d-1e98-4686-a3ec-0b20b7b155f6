// 主页面类
class MainPage {
    constructor(gameManager) {
        this.gameManager = gameManager;
        this.canvas = gameManager.canvas;
        this.ctx = gameManager.ctx;
        
        // 背景效果
        this.backgroundScene = null;
        
        // 按钮配置
        this.buttons = [
            {
                id: 'start',
                text: '开始游戏',
                x: 0,
                y: 0,
                width: 200,
                height: 60,
                color: '#4CAF50',
                hoverColor: '#45a049'
            },
            {
                id: 'rank',
                text: '排行榜',
                x: 0,
                y: 0,
                width: 200,
                height: 60,
                color: '#2196F3',
                hoverColor: '#1976D2'
            },
            {
                id: 'setting',
                text: '设置',
                x: 0,
                y: 0,
                width: 200,
                height: 60,
                color: '#FF9800',
                hoverColor: '#F57C00'
            }
        ];
        
        // 动画状态
        this.titleAnimation = 0;
        // 移除按钮动画状态
        
        console.log('MainPage初始化完成');
    }
    
    // 初始化主页面
    init() {
        console.log('初始化主页面');
        
        // 初始化背景效果
        this.initBackgroundEffects();
        
        // 计算按钮位置
        this.calculateButtonPositions();
        
        // 设置触摸事件监听
        this.setupTouchEvents();
        
        console.log('主页面初始化完成');
    }
    
    // 初始化背景效果
    initBackgroundEffects() {
        try {
            if (typeof BackgroundUtils !== 'undefined') {
                this.backgroundScene = BackgroundUtils.createBackgroundScene(
                    this.canvas.width, 
                    this.canvas.height
                );
                console.log('背景效果初始化成功');
            } else {
                console.warn('BackgroundUtils未加载，使用简化背景');
                this.backgroundScene = null;
            }
        } catch (error) {
            console.warn('背景效果初始化失败:', error);
            this.backgroundScene = null;
        }
    }
    
    // 计算按钮位置
    calculateButtonPositions() {
        const centerX = this.canvas.width / 2;
        const startY = this.canvas.height / 2 + 50;
        const spacing = 80;
        
        this.buttons.forEach((button, index) => {
            button.x = centerX - button.width / 2;
            button.y = startY + index * spacing;
        });
    }
    
    // 处理触摸开始事件
    handleTouchStart(x, y) {
        // 检查按钮点击
        this.buttons.forEach(button => {
            if (this.isPointInButton(x, y, button)) {
                this.handleButtonClick(button.id);
            }
        });
    }
    
    // 检查点是否在按钮内
    isPointInButton(x, y, button) {
        return x >= button.x && x <= button.x + button.width &&
               y >= button.y && y <= button.y + button.height;
    }
    
    // 处理按钮点击
    handleButtonClick(buttonId) {
        console.log(`按钮点击: ${buttonId}`);
        
        // 播放点击音效
        if (this.gameManager.audioManager) {
            this.gameManager.audioManager.playSound('click');
        }
        
        switch (buttonId) {
            case 'start':
                // 开始游戏，使用默认关卡配置
                const levelConfig = {
                    targetScore: 1000,
                    name: '萌宠新手村',
                    level: 1
                };
                this.gameManager.switchToPage('game', levelConfig);
                break;
                
            case 'rank':
                this.gameManager.switchToPage('rank');
                break;
                
            case 'setting':
                this.gameManager.switchToPage('setting');
                break;
                
            default:
                console.warn(`未知按钮: ${buttonId}`);
        }
    }
    
    // 更新主页面状态
    update(deltaTime) {
        // 更新动画（只保留标题动画）
        this.titleAnimation += 0.02;
        // 移除按钮动画更新
        
        // 更新背景效果
        if (this.backgroundScene && typeof BackgroundUtils !== 'undefined') {
            try {
                BackgroundUtils.updateBackgroundScene(
                    this.backgroundScene, 
                    this.canvas.width, 
                    this.canvas.height
                );
            } catch (error) {
                console.warn('背景更新失败:', error);
            }
        }
    }
    
    // 渲染主页面
    render() {
        // 清空画布
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 渲染背景
        this.renderBackground();
        
        // 渲染标题
        this.renderTitle();
        
        // 渲染按钮
        this.renderButtons();
        
        // 渲染版本信息
        this.renderVersionInfo();
    }
    
    // 渲染背景
    renderBackground() {
        if (this.backgroundScene && typeof BackgroundUtils !== 'undefined') {
            try {
                BackgroundUtils.renderBackgroundScene(
                    this.ctx, 
                    this.backgroundScene, 
                    this.canvas.width, 
                    this.canvas.height
                );
            } catch (error) {
                console.warn('背景渲染失败:', error);
                this.renderSimpleBackground();
            }
        } else {
            this.renderSimpleBackground();
        }
    }
    
    // 渲染简化背景
    renderSimpleBackground() {
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        gradient.addColorStop(0, '#87CEEB');
        gradient.addColorStop(0.5, '#98FB98');
        gradient.addColorStop(1, '#DDA0DD');
        
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }
    
    // 渲染标题
    renderTitle() {
        const centerX = this.canvas.width / 2;
        const titleY = this.canvas.height / 3;
        
        // 标题阴影效果
        this.ctx.save();
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        this.ctx.shadowBlur = 10;
        this.ctx.shadowOffsetX = 3;
        this.ctx.shadowOffsetY = 3;
        
        // 主标题
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = 'bold 48px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        
        // 添加动画效果
        const scale = 1 + Math.sin(this.titleAnimation) * 0.05;
        this.ctx.save();
        this.ctx.translate(centerX, titleY);
        this.ctx.scale(scale, scale);
        this.ctx.fillText('休闲消消消', 0, 0);
        this.ctx.restore();
        
        // 副标题
        this.ctx.fillStyle = '#FFD700';
        this.ctx.font = 'bold 24px Arial, "Microsoft YaHei"';
        this.ctx.fillText('萌宠消除大作战', centerX, titleY + 60);
        
        this.ctx.restore();
    }
    
    // 渲染按钮
    renderButtons() {
        this.buttons.forEach((button, index) => {
            // 移除按钮动画效果，使用固定位置
            const buttonY = button.y;
            const cornerRadius = 15; // 圆角半径

            // 绘制按钮阴影
            this.ctx.save();
            this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
            this.ctx.shadowBlur = 8;
            this.ctx.shadowOffsetX = 2;
            this.ctx.shadowOffsetY = 2;

            // 绘制圆角按钮背景
            this.ctx.fillStyle = button.color;
            this.drawRoundedRect(button.x, buttonY, button.width, button.height, cornerRadius);
            this.ctx.fill();

            this.ctx.restore();

            // 绘制圆角按钮边框
            this.ctx.strokeStyle = button.hoverColor;
            this.ctx.lineWidth = 3;
            this.drawRoundedRect(button.x, buttonY, button.width, button.height, cornerRadius);
            this.ctx.stroke();

            // 绘制按钮文字
            this.ctx.fillStyle = '#FFFFFF';
            this.ctx.font = 'bold 20px Arial, "Microsoft YaHei"';
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';
            this.ctx.fillText(
                button.text,
                button.x + button.width / 2,
                buttonY + button.height / 2
            );
        });
    }

    // 绘制圆角矩形
    drawRoundedRect(x, y, width, height, radius) {
        this.ctx.beginPath();
        this.ctx.moveTo(x + radius, y);
        this.ctx.lineTo(x + width - radius, y);
        this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        this.ctx.lineTo(x + width, y + height - radius);
        this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        this.ctx.lineTo(x + radius, y + height);
        this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        this.ctx.lineTo(x, y + radius);
        this.ctx.quadraticCurveTo(x, y, x + radius, y);
        this.ctx.closePath();
    }
    
    // 渲染版本信息
    renderVersionInfo() {
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
        this.ctx.font = '14px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'right';
        this.ctx.textBaseline = 'bottom';
        this.ctx.fillText('v1.0.0', this.canvas.width - 20, this.canvas.height - 20);
    }
    
    // 设置触摸事件监听
    setupTouchEvents() {
        console.log('设置触摸事件监听器...');
        
        // 抖音小游戏触摸事件处理
        if (typeof tt !== 'undefined') {
            console.log('使用抖音小游戏触摸事件API');
            
            // 使用抖音小游戏的全局触摸事件API
            this.touchStartHandler = (e) => {
                console.log('触摸开始事件:', e);
                if (e.touches && e.touches.length > 0) {
                    const touch = e.touches[0];
                    this.handleTouchStart(touch.clientX, touch.clientY);
                }
            };
            
            this.touchEndHandler = (e) => {
                console.log('触摸结束事件:', e);
                // 可以在这里处理触摸结束逻辑
            };
            
            // 使用抖音小游戏的全局触摸事件
            tt.onTouchStart(this.touchStartHandler);
            tt.onTouchEnd(this.touchEndHandler);
            
            console.log('抖音小游戏触摸事件监听器设置完成');
        } else if (this.canvas && typeof this.canvas.addEventListener === 'function') {
            console.log('使用浏览器触摸事件API');
            
            // 浏览器环境的事件处理
            this.touchStartHandler = (e) => {
                e.preventDefault();
                const touch = e.touches[0];
                const rect = this.canvas.getBoundingClientRect();
                const x = touch.clientX - rect.left;
                const y = touch.clientY - rect.top;
                this.handleTouchStart(x, y);
            };
            
            this.canvas.addEventListener('touchstart', this.touchStartHandler);
            
            // 同时支持鼠标点击（用于调试）
            this.clickHandler = (e) => {
                const rect = this.canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                this.handleTouchStart(x, y);
            };
            
            this.canvas.addEventListener('click', this.clickHandler);
            console.log('浏览器触摸事件监听器设置完成');
        } else {
            console.warn('无法设置触摸事件监听器，canvas不支持addEventListener');
        }
    }
    
    // 处理窗口大小变化
    handleResize() {
        this.calculateButtonPositions();
        
        // 重新初始化背景效果
        if (this.backgroundScene) {
            this.initBackgroundEffects();
        }
    }
    
    // 清理资源
    destroy() {
        console.log('清理MainPage资源');
        this.destroyed = true;
        this.backgroundScene = null;
        
        // 移除事件监听器
        if (typeof tt !== 'undefined') {
            // 抖音小游戏环境清理
            if (this.touchStartHandler) {
                tt.offTouchStart(this.touchStartHandler);
            }
            if (this.touchEndHandler) {
                tt.offTouchEnd(this.touchEndHandler);
            }
            console.log('抖音小游戏事件监听器已清理');
        } else if (this.canvas && typeof this.canvas.removeEventListener === 'function') {
            // 浏览器环境清理
            if (this.touchStartHandler) {
                this.canvas.removeEventListener('touchstart', this.touchStartHandler);
            }
            if (this.clickHandler) {
                this.canvas.removeEventListener('click', this.clickHandler);
            }
            console.log('浏览器事件监听器已清理');
        }
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MainPage;
} else {
    window.MainPage = MainPage;
}
