/**
 * 游戏页面匹配器 - 处理方块匹配和消除逻辑
 */
class GamePageMatcher {
    constructor(core) {
        this.core = core;
        this.canvas = core.canvas;
        this.ctx = core.ctx;
        
        // 匹配配置
        this.minMatchLength = 3; // 最小匹配长度
        this.matchDelay = 200; // 匹配检测延迟
        
        // 匹配状态
        this.isProcessingMatches = false;
        this.pendingMatches = [];
        this.matchHistory = [];
        
        console.log('GamePageMatcher初始化完成');
    }
    
    // 检查所有匹配
    findAllMatches() {
        const matches = [];
        const visited = new Set();
        
        // 检查水平匹配
        const horizontalMatches = this.findHorizontalMatches();
        horizontalMatches.forEach(match => {
            match.blocks.forEach(block => {
                const key = `${block.row}-${block.col}`;
                if (!visited.has(key)) {
                    matches.push(block);
                    visited.add(key);
                }
            });
        });
        
        // 检查垂直匹配
        const verticalMatches = this.findVerticalMatches();
        verticalMatches.forEach(match => {
            match.blocks.forEach(block => {
                const key = `${block.row}-${block.col}`;
                if (!visited.has(key)) {
                    matches.push(block);
                    visited.add(key);
                }
            });
        });
        
        return {
            matches: matches,
            horizontalGroups: horizontalMatches,
            verticalGroups: verticalMatches,
            totalCount: matches.length
        };
    }
    
    // 查找水平匹配
    findHorizontalMatches() {
        const matches = [];
        
        for (let row = 0; row < this.core.gridSizeY; row++) {
            let currentMatch = [];
            let currentType = null;
            
            for (let col = 0; col < this.core.gridSizeX; col++) {
                const block = this.core.grid[row] && this.core.grid[row][col];
                
                if (block && !block.isRemoved && block.type === currentType) {
                    // 继续当前匹配
                    currentMatch.push({ row, col, type: block.type, block });
                } else {
                    // 检查当前匹配是否足够长
                    if (currentMatch.length >= this.minMatchLength) {
                        const match = {
                            type: 'horizontal',
                            blocks: [...currentMatch],
                            length: currentMatch.length,
                            row: row,
                            startCol: currentMatch[0].col,
                            endCol: currentMatch[currentMatch.length - 1].col
                        };
                        matches.push(match);
                        console.log(`检测到水平${currentMatch.length}连消: 行${row}, 列${match.startCol}-${match.endCol}, 类型${currentMatch[0].type}`);
                    }
                    
                    // 开始新的匹配
                    if (block && !block.isRemoved) {
                        currentMatch = [{ row, col, type: block.type, block }];
                        currentType = block.type;
                    } else {
                        currentMatch = [];
                        currentType = null;
                    }
                }
            }
            
            // 检查行末的匹配
            if (currentMatch.length >= this.minMatchLength) {
                const match = {
                    type: 'horizontal',
                    blocks: [...currentMatch],
                    length: currentMatch.length,
                    row: row,
                    startCol: currentMatch[0].col,
                    endCol: currentMatch[currentMatch.length - 1].col
                };
                matches.push(match);
                console.log(`检测到水平${currentMatch.length}连消(行末): 行${row}, 列${match.startCol}-${match.endCol}, 类型${currentMatch[0].type}`);
            }
        }
        
        return matches;
    }
    
    // 查找垂直匹配
    findVerticalMatches() {
        const matches = [];
        
        for (let col = 0; col < this.core.gridSizeX; col++) {
            let currentMatch = [];
            let currentType = null;
            
            for (let row = 0; row < this.core.gridSizeY; row++) {
                const block = this.core.grid[row] && this.core.grid[row][col];
                
                if (block && !block.isRemoved && block.type === currentType) {
                    // 继续当前匹配
                    currentMatch.push({ row, col, type: block.type, block });
                } else {
                    // 检查当前匹配是否足够长
                    if (currentMatch.length >= this.minMatchLength) {
                        const match = {
                            type: 'vertical',
                            blocks: [...currentMatch],
                            length: currentMatch.length,
                            col: col,
                            startRow: currentMatch[0].row,
                            endRow: currentMatch[currentMatch.length - 1].row
                        };
                        matches.push(match);
                        console.log(`检测到垂直${currentMatch.length}连消: 列${col}, 行${match.startRow}-${match.endRow}, 类型${currentMatch[0].type}`);
                    }
                    
                    // 开始新的匹配
                    if (block && !block.isRemoved) {
                        currentMatch = [{ row, col, type: block.type, block }];
                        currentType = block.type;
                    } else {
                        currentMatch = [];
                        currentType = null;
                    }
                }
            }
            
            // 检查列末的匹配
            if (currentMatch.length >= this.minMatchLength) {
                matches.push({
                    type: 'vertical',
                    blocks: [...currentMatch],
                    length: currentMatch.length,
                    col: col,
                    startRow: currentMatch[0].row,
                    endRow: currentMatch[currentMatch.length - 1].row
                });
            }
        }
        
        return matches;
    }
    
    // 处理匹配消除 - 使用GamePageCore的新逻辑
    processMatches(matchResult) {
        if (!matchResult || matchResult.matches.length === 0) {
            return Promise.resolve(0);
        }

        console.log(`处理${matchResult.matches.length}个匹配方块`);

        this.isProcessingMatches = true;

        return new Promise((resolve) => {
            // 调整matchResult结构以匹配GamePageCore.processMatches期望的结构
            // 合并水平和垂直匹配组
            const matchGroups = [
                ...(matchResult.horizontalGroups || []),
                ...(matchResult.verticalGroups || [])
            ];

            const adjustedMatchResult = {
                matches: matchResult.matches,
                matchGroups: matchGroups
            };

            console.log('调整后的匹配结果:', adjustedMatchResult);
            console.log('匹配组数量:', matchGroups.length);
            if (matchGroups.length > 0) {
                console.log('第一个匹配组结构:', matchGroups[0]);
            }
            
            // 使用GamePageCore的新处理逻辑
            const result = this.core.processMatches(adjustedMatchResult);

            // 标记方块为已匹配
            this.markBlocksAsMatched(matchResult.matches);

            // 创建消除特效
            this.createMatchEffects(matchResult);

            // 生成特殊方块 - 在移除方块之前生成
            if (result.specialBlocks && result.specialBlocks.length > 0) {
                result.specialBlocks.forEach(special => {
                    console.log(`准备生成特殊方块: ${special.type} 在位置 (${special.row}, ${special.col})`);

                    // 标记该位置需要生成特殊方块
                    if (!this.pendingSpecialBlocks) {
                        this.pendingSpecialBlocks = [];
                    }
                    this.pendingSpecialBlocks.push(special);
                });
            }

            // 延迟后移除方块
            setTimeout(() => {
                const removedPositions = this.removeMatchedBlocks(matchResult.matches);
                this.isProcessingMatches = false;
                resolve({
                    score: result.score,
                    removedPositions: removedPositions
                });
            }, 300);
        });
    }
    
    // 计算匹配分数
    calculateScore(matchResult) {
        let totalScore = 0;
        
        // 基础分数
        const baseScore = matchResult.matches.length * 100;
        
        // 长匹配奖励
        let lengthBonus = 0;
        
        // 检查是否有水平和垂直组
        if (matchResult.horizontalGroups && matchResult.verticalGroups) {
            [...matchResult.horizontalGroups, ...matchResult.verticalGroups].forEach(group => {
                if (group.length >= 4) {
                    lengthBonus += (group.length - 3) * 50;
                }
                if (group.length >= 5) {
                    lengthBonus += (group.length - 4) * 100;
                }
            });
        }
        
        // 特殊形状奖励（L形、T形等）
        const shapeBonus = this.calculateShapeBonus(matchResult);
        
        totalScore = baseScore + lengthBonus + shapeBonus;
        
        console.log(`分数计算: 基础${baseScore} + 长度${lengthBonus} + 形状${shapeBonus} = ${totalScore}`);
        
        return totalScore;
    }
    
    // 计算特殊形状奖励
    calculateShapeBonus(matchResult) {
        // 这里可以实现L形、T形等特殊形状的检测
        // 暂时返回0，后续可以扩展
        return 0;
    }
    
    // 标记方块为已匹配
    markBlocksAsMatched(matches) {
        matches.forEach(match => {
            const block = this.core.grid[match.row] && this.core.grid[match.row][match.col];
            if (block) {
                block.isMatched = true;
                block.matchTime = Date.now();
                
                // 开始消失动画
                this.startDisappearAnimation(block);
            }
        });
    }
    
    // 开始消失动画
    startDisappearAnimation(block) {
        const startScale = 1;
        const targetScale = 0;
        const startAlpha = 1;
        const targetAlpha = 0;
        const duration = 300;
        
        const startTime = Date.now();
        
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // 使用缓动函数
            const easeProgress = this.easeInQuad(progress);
            
            block.scale = startScale + (targetScale - startScale) * easeProgress;
            block.alpha = startAlpha + (targetAlpha - startAlpha) * progress;
            block.rotation += 0.1; // 旋转效果
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        animate();
    }
    
    // 缓动函数
    easeInQuad(t) {
        return t * t;
    }
    
    // 创建匹配特效
    createMatchEffects(matchResult) {
        matchResult.matches.forEach(match => {
            const block = this.core.grid[match.row] && this.core.grid[match.row][match.col];
            if (block) {
                // 粒子爆炸效果
                this.core.createParticles(
                    block.x + this.core.blockSize / 2,
                    block.y + this.core.blockSize / 2,
                    block.color,
                    12
                );
                
                // 闪烁星星效果
                this.core.createSparkles(
                    block.x + this.core.blockSize / 2,
                    block.y + this.core.blockSize / 2,
                    6
                );
            }
        });
        
        // 移除了连击文字效果
    }
    
    // 移除已匹配的方块
    removeMatchedBlocks(matches) {
        const removedPositions = [];
        
        matches.forEach(match => {
            if (this.core.grid[match.row] && this.core.grid[match.row][match.col]) {
                // 标记为已移除
                this.core.grid[match.row][match.col].isRemoved = true;
                this.core.grid[match.row][match.col] = null;
                
                removedPositions.push({
                    row: match.row,
                    col: match.col,
                    type: match.type
                });
            }
        });
        
        console.log(`移除了${removedPositions.length}个方块`);

        // 生成待生成的特殊方块
        if (this.pendingSpecialBlocks && this.pendingSpecialBlocks.length > 0) {
            this.pendingSpecialBlocks.forEach(special => {
                // 检查位置是否已被清空
                if (!this.core.grid[special.row][special.col]) {
                    const specialBlock = this.core.createSpecialBlock(
                        special.row,
                        special.col,
                        this.core.gridStartX,
                        this.core.gridStartY,
                        special.type
                    );

                    this.core.grid[special.row][special.col] = specialBlock;

                    // 消除生成特殊方块时不播放音频（根据用户要求移除音效）
                    console.log(`生成特殊方块: ${special.type} 在位置 (${special.row}, ${special.col})`);
                }
            });

            // 清空待生成列表
            this.pendingSpecialBlocks = [];
        }

        // 记录匹配历史
        this.matchHistory.push({
            timestamp: Date.now(),
            matches: matches.length,
            positions: removedPositions,
            score: this.calculateScore({ matches })
        });

        return removedPositions;
    }
    
    // 检查是否有可能的匹配
    hasPossibleMatches() {
        // 简单检查：尝试每个可能的交换
        for (let row = 0; row < this.core.gridSizeY; row++) {
            for (let col = 0; col < this.core.gridSizeX; col++) {
                // 检查右边的交换
                if (col < this.core.gridSizeX - 1) {
                    if (this.wouldCreateMatch(row, col, row, col + 1)) {
                        return true;
                    }
                }
                
                // 检查下面的交换
                if (row < this.core.gridSizeY - 1) {
                    if (this.wouldCreateMatch(row, col, row + 1, col)) {
                        return true;
                    }
                }
            }
        }
        
        return false;
    }
    
    // 检查交换是否会产生匹配
    wouldCreateMatch(row1, col1, row2, col2) {
        const block1 = this.core.grid[row1] && this.core.grid[row1][col1];
        const block2 = this.core.grid[row2] && this.core.grid[row2][col2];
        
        if (!block1 || !block2 || block1.isRemoved || block2.isRemoved) {
            return false;
        }
        
        // 临时交换
        this.core.grid[row1][col1] = block2;
        this.core.grid[row2][col2] = block1;
        
        // 检查是否产生匹配
        const hasMatch = this.checkPositionForMatch(row1, col1) || 
                        this.checkPositionForMatch(row2, col2);
        
        // 恢复原状
        this.core.grid[row1][col1] = block1;
        this.core.grid[row2][col2] = block2;
        
        return hasMatch;
    }
    
    // 检查指定位置是否形成匹配
    checkPositionForMatch(row, col) {
        const block = this.core.grid[row] && this.core.grid[row][col];
        if (!block || block.isRemoved) {
            return false;
        }
        
        const type = block.type;
        
        // 检查水平匹配
        let horizontalCount = 1;
        
        // 向左检查
        for (let c = col - 1; c >= 0; c--) {
            const leftBlock = this.core.grid[row] && this.core.grid[row][c];
            if (leftBlock && !leftBlock.isRemoved && leftBlock.type === type) {
                horizontalCount++;
            } else {
                break;
            }
        }
        
        // 向右检查
        for (let c = col + 1; c < this.core.gridSizeX; c++) {
            const rightBlock = this.core.grid[row] && this.core.grid[row][c];
            if (rightBlock && !rightBlock.isRemoved && rightBlock.type === type) {
                horizontalCount++;
            } else {
                break;
            }
        }
        
        if (horizontalCount >= this.minMatchLength) {
            return true;
        }
        
        // 检查垂直匹配
        let verticalCount = 1;
        
        // 向上检查
        for (let r = row - 1; r >= 0; r--) {
            const upBlock = this.core.grid[r] && this.core.grid[r][col];
            if (upBlock && !upBlock.isRemoved && upBlock.type === type) {
                verticalCount++;
            } else {
                break;
            }
        }
        
        // 向下检查
        for (let r = row + 1; r < this.core.gridSizeY; r++) {
            const downBlock = this.core.grid[r] && this.core.grid[r][col];
            if (downBlock && !downBlock.isRemoved && downBlock.type === type) {
                verticalCount++;
            } else {
                break;
            }
        }
        
        return verticalCount >= this.minMatchLength;
    }
    
    // 获取匹配提示
    getMatchHints() {
        const hints = [];
        
        for (let row = 0; row < this.core.gridSizeY; row++) {
            for (let col = 0; col < this.core.gridSizeX; col++) {
                // 检查右边的交换
                if (col < this.core.gridSizeX - 1) {
                    if (this.wouldCreateMatch(row, col, row, col + 1)) {
                        hints.push({
                            from: { row, col },
                            to: { row, col: col + 1 },
                            type: 'horizontal'
                        });
                    }
                }
                
                // 检查下面的交换
                if (row < this.core.gridSizeY - 1) {
                    if (this.wouldCreateMatch(row, col, row + 1, col)) {
                        hints.push({
                            from: { row, col },
                            to: { row: row + 1, col },
                            type: 'vertical'
                        });
                    }
                }
            }
        }
        
        return hints;
    }
    
    // 检查是否正在处理匹配
    isProcessing() {
        return this.isProcessingMatches;
    }
    
    // 获取匹配统计
    getMatchStats() {
        return {
            totalMatches: this.matchHistory.length,
            isProcessing: this.isProcessingMatches,
            pendingCount: this.pendingMatches.length,
            history: this.matchHistory.slice(-10) // 最近10次匹配
        };
    }
    
    // 清理匹配器
    clear() {
        this.isProcessingMatches = false;
        this.pendingMatches = [];
        this.matchHistory = [];
    }
    
    // 重置匹配器
    reset() {
        this.clear();
        console.log('匹配器已重置');
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GamePageMatcher;
} else {
    window.GamePageMatcher = GamePageMatcher;
}
