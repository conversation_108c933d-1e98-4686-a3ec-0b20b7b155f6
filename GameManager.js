/**
 * 游戏管理器类
 * 负责画布管理、模块加载和页面切换
 */
class GameManager {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        this.currentPage = null;
        this.gameData = {
            settings: {
                bgmVolume: 0.5,
                effectVolume: 0.7,
                mute: false,
                muteMode: false,
                autoSave: true,
                showTips: true
            },
            playerData: {
                bestScore: 0,
                currentLevel: 1,
                totalGames: 0
            },
            rankData: []
        };

        // 模块缓存
        this.loadedModules = {};

        // 音效管理器
        this.audioManager = null;

        this.init();
    }

    init() {
        console.log('初始化萌宠爱消消游戏...');
        this.createCanvas();
        this.loadGameData()
            .then(() => this.loadCoreModules())
            .then(() => this.switchToPage('main'))
            .then(() => {
                this.startGameLoop();
                console.log('游戏管理器初始化完成');
            })
            .catch(error => {
                console.error('游戏初始化失败:', error);
                // 即使初始化失败，也尝试启动基本的主页面
                this.createSimpleMainPage();
                this.startGameLoop();
            });
    }

    createCanvas() {
        this.canvas = tt.createCanvas();
        this.ctx = this.canvas.getContext('2d');
        const systemInfo = tt.getSystemInfoSync();
        this.canvas.width = systemInfo.windowWidth;
        this.canvas.height = systemInfo.windowHeight;

        console.log(`画布尺寸: ${this.canvas.width} x ${this.canvas.height}`);
    }

    loadGameData() {
        return new Promise((resolve) => {
            try {
                const savedSettings = tt.getStorageSync('gameSettings');
                if (savedSettings) {
                    this.gameData.settings = { ...this.gameData.settings, ...JSON.parse(savedSettings) };
                    console.log('从缓存加载用户设置');
                } else {
                    // 如果没有缓存设置，写入默认配置
                    const defaultSettings = {
                        bgmVolume: 0.5,
                        effectVolume: 0.5,
                        mute: false,
                        muteMode: false,
                        autoSave: true,
                        showTips: true
                    };
                    this.gameData.settings = { ...this.gameData.settings, ...defaultSettings };

                    // 保存默认设置到缓存
                    tt.setStorageSync('gameSettings', JSON.stringify(defaultSettings));
                    console.log('写入默认游戏设置到缓存');
                }

                const savedPlayerData = tt.getStorageSync('playerData');
                if (savedPlayerData) {
                    this.gameData.playerData = { ...this.gameData.playerData, ...JSON.parse(savedPlayerData) };
                }
                console.log('游戏数据加载完成');
                resolve();
            } catch (error) {
                console.error('加载游戏数据失败:', error);
                // 即使失败也要确保有默认设置
                this.gameData.settings = {
                    bgmVolume: 0.5,
                    effectVolume: 0.5,
                    mute: false,
                    muteMode: false,
                    autoSave: true,
                    showTips: true
                };
                resolve();
            }
        });
    }

    saveGameData() {
        try {
            tt.setStorageSync('gameSettings', JSON.stringify(this.gameData.settings));
            tt.setStorageSync('playerData', JSON.stringify(this.gameData.playerData));
            console.log('游戏数据已保存');
        } catch (error) {
            console.error('保存游戏数据失败:', error);
        }
    }

    // 启动游戏循环
    startGameLoop() {
        console.log('启动游戏循环...');

        const gameLoop = () => {
            try {
                // 清空画布
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

                // 更新当前页面
                if (this.currentPage) {
                    if (typeof this.currentPage.update === 'function') {
                        this.currentPage.update();
                    }
                    if (typeof this.currentPage.render === 'function') {
                        this.currentPage.render();
                    }
                }

                // 请求下一帧
                requestAnimationFrame(gameLoop);
            } catch (error) {
                console.error('游戏循环错误:', error);
                // 继续循环，避免游戏卡死
                requestAnimationFrame(gameLoop);
            }
        };

        // 启动循环
        requestAnimationFrame(gameLoop);
        console.log('游戏循环已启动');
    }

    loadCoreModules() {
        console.log('开始加载核心模块...');

        return Promise.resolve()
            .then(() => this.loadModule('GameUIConfig', './config/GameUIConfig.js'))
            .then(() => this.loadModule('CuteBackground', './utils/CuteBackground.js'))
            .then(() => this.loadModule('SimpleMainPage', './pages/SimpleMainPage.js'))
            .then(() => this.loadModule('SimpleRankPage', './pages/SimpleRankPage.js'))
            .then(() => this.loadModule('SimpleSettingPage', './pages/SimpleSettingPage.js'))
            .then(() => this.loadModule('SimpleGamePage', './pages/SimpleGamePage.js'))
            .then(() => this.loadModule('BackgroundUtils', './utils/BackgroundUtils.js'))
            .then(() => this.loadModule('AudioManager', './utils/AudioManager.js'))
            .then(() => {
                // 创建UI配置实例
                let uiConfig = null;
                if (typeof GameUIConfig !== 'undefined') {
                    uiConfig = new GameUIConfig();
                    console.log('UI配置创建成功');
                } else {
                    console.warn('GameUIConfig模块未加载，将使用默认配置');
                }

                // 创建音频管理器，传入UI配置
                if (typeof AudioManager !== 'undefined') {
                    this.audioManager = new AudioManager(uiConfig);
                    // 预加载音频文件
                    this.audioManager.preloadAudio().catch(error => {
                        console.warn('音频预加载失败:', error);
                    });
                    console.log('音频管理器创建成功');
                } else {
                    console.warn('AudioManager模块未加载，音效将不可用');
                }
                console.log('核心模块加载完成');
            })
            .catch(error => {
                console.error('加载核心模块失败:', error);
            });
    }

    // 创建简化的主页面
    createSimpleMainPage() {
        console.log('创建简化主页面');
        try {
            // 如果模块已加载，使用模块中的类
            if (typeof SimpleMainPage !== 'undefined') {
                this.currentPage = new SimpleMainPage(this);
            } else {
                // 回退到内联类
                console.warn('SimpleMainPage模块未加载，使用内联版本');
                this.currentPage = this.createInlineMainPage();
            }
            this.currentPage.init();
            console.log('简化主页面创建成功');
        } catch (error) {
            console.error('创建简化主页面失败:', error);
        }
    }

    // 内联主页面类（备用方案）
    createInlineMainPage() {
        return {
            gameManager: this,
            canvas: this.canvas,
            ctx: this.ctx,
            init: function() { console.log('内联主页面初始化'); },
            update: function() {},
            render: function() {
                this.ctx.fillStyle = '#87CEEB';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                this.ctx.fillStyle = '#FFFFFF';
                this.ctx.font = 'bold 48px Arial';
                this.ctx.textAlign = 'center';
                this.ctx.fillText('休闲消消消', this.canvas.width / 2, this.canvas.height / 2);
            },
            destroy: function() {}
        };
    }

    switchToPage(pageName, ...args) {
        console.log(`切换到页面: ${pageName}`);

        // 销毁当前页面
        if (this.currentPage && typeof this.currentPage.destroy === 'function') {
            this.currentPage.destroy();
        }

        return new Promise((resolve, reject) => {
            try {
                switch (pageName) {
                    case 'main':
                        this.currentPage = new (this.getPageClass('SimpleMainPage'))(this);
                        this.currentPage.init();
                        resolve();
                        break;

                    case 'game':
                        // 加载美化版游戏页面
                        this.loadBeautifulGamePage()
                            .then(() => {
                                const gameConfig = args[0] || {
                                    targetScore: 1000,
                                    name: '萌宠新手村',
                                    level: 1
                                };

                                // 创建美化版游戏页面实例
                                if (typeof BeautifulGamePage !== 'undefined') {
                                    this.currentPage = new BeautifulGamePage(this, gameConfig);
                                    this.currentPage.init();
                                    resolve();
                                } else {
                                    console.warn('BeautifulGamePage类未找到，使用简化版');
                                    this.currentPage = new (this.getPageClass('SimpleGamePage'))(this, gameConfig);
                                    this.currentPage.init();
                                    resolve();
                                }
                            })
                            .catch(error => {
                                console.error('加载美化版游戏页面失败:', error);
                                // 回退到简化版
                                const gameConfig = args[0] || {
                                    targetScore: 1000,
                                    name: '萌宠新手村',
                                    level: 1
                                };
                                this.currentPage = new (this.getPageClass('SimpleGamePage'))(this, gameConfig);
                                this.currentPage.init();
                                resolve();
                            });
                        break;

                    case 'rank':
                        // 尝试加载美化版排行榜页面
                        this.loadBeautifulRankPage()
                            .then(() => {
                                if (typeof BeautifulRankPage !== 'undefined') {
                                    this.currentPage = new BeautifulRankPage(this);
                                    this.currentPage.init();
                                    resolve();
                                } else {
                                    console.warn('BeautifulRankPage类未找到，使用简化版');
                                    this.currentPage = new (this.getPageClass('SimpleRankPage'))(this);
                                    this.currentPage.init();
                                    resolve();
                                }
                            })
                            .catch(error => {
                                console.error('加载美化版排行榜页面失败:', error);
                                // 回退到简化版
                                this.currentPage = new (this.getPageClass('SimpleRankPage'))(this);
                                this.currentPage.init();
                                resolve();
                            });
                        break;

                    case 'setting':
                    case 'setting':
                        // 尝试加载美化版设置页面
                        this.loadBeautifulSettingPage()
                            .then(() => {
                                if (typeof BeautifulSettingPage !== 'undefined') {
                                    this.currentPage = new BeautifulSettingPage(this);
                                    resolve();
                                } else {
                                    console.warn('BeautifulSettingPage类未找到，使用简化版');
                                    this.currentPage = new (this.getPageClass('SimpleSettingPage'))(this);
                                    this.currentPage.init();
                                    resolve();
                                }
                            })
                            .catch(error => {
                                console.error('加载美化版设置页面失败:', error);
                                // 回退到简化版
                                this.currentPage = new (this.getPageClass('SimpleSettingPage'))(this);
                                this.currentPage.init();
                                resolve();
                            });
                        break;

                    default:
                        console.error(`未知页面: ${pageName}`);
                        reject(new Error(`未知页面: ${pageName}`));
                }
            } catch (error) {
                console.error(`切换到页面 ${pageName} 失败:`, error);
                reject(error);
            }
        });
    }

    // 获取页面类
    getPageClass(className) {
        // 尝试从全局作用域获取
        if (typeof window !== 'undefined' && window[className]) {
            return window[className];
        }
        if (typeof globalThis !== 'undefined' && globalThis[className]) {
            return globalThis[className];
        }
        if (typeof global !== 'undefined' && global[className]) {
            return global[className];
        }

        // 从模块缓存获取
        if (this.loadedModules[className]) {
            return this.loadedModules[className];
        }

        console.error(`页面类 ${className} 未找到`);
        throw new Error(`页面类 ${className} 未找到`);
    }

    loadBeautifulGamePage() {
        console.log('开始加载美化版游戏页面模块...');

        return Promise.resolve()
            .then(() => this.loadModule('GameUIConfig', './config/GameUIConfig.js'))
            .then(() => this.loadModule('GamePageCore', './pages/GamePageCore.js'))
            .then(() => this.loadModule('GamePageRenderer', './pages/GamePageRenderer.js'))
            .then(() => this.loadModule('GamePageEvents', './pages/GamePageEvents.js'))
            .then(() => this.loadModule('GamePageFalling', './pages/GamePageFalling.js'))
            .then(() => this.loadModule('GamePageGenerator', './pages/GamePageGenerator.js'))
            .then(() => this.loadModule('GamePageMatcher', './pages/GamePageMatcher.js'))
            .then(() => this.loadModule('GamePageAnimator', './pages/GamePageAnimator.js'))
            .then(() => this.loadModule('BeautifulGamePage', './pages/BeautifulGamePage.js'))
            .then(() => {
                console.log('美化版游戏页面模块加载完成');
            })
            .catch(error => {
                console.error('加载美化版游戏页面模块失败:', error);
                throw error;
            });
    }

    loadBeautifulRankPage() {
        console.log('开始加载美化版排行榜页面模块...');

        return Promise.resolve()
            .then(() => this.loadModule('RankPageCore', './pages/RankPageCore.js'))
            .then(() => this.loadModule('RankPageRenderer', './pages/RankPageRenderer.js'))
            .then(() => this.loadModule('RankPageEvents', './pages/RankPageEvents.js'))
            .then(() => this.loadModule('BeautifulRankPage', './pages/BeautifulRankPage.js'))
            .then(() => {
                console.log('美化版排行榜页面模块加载完成');
            })
            .catch(error => {
                console.error('加载美化版排行榜页面模块失败:', error);
                throw error;
            });
    }

    loadBeautifulSettingPage() {
        console.log('开始加载美化版设置页面模块...');

        return Promise.resolve()
            .then(() => this.loadModule('BeautifulSettingPage', './pages/BeautifulSettingPage.js'))
            .then(() => {
                console.log('美化版设置页面模块加载完成');
            })
            .catch(error => {
                console.error('加载美化版设置页面模块失败:', error);
                throw error;
            });
    }

    loadModule(moduleName, modulePath) {
        if (this.loadedModules[moduleName]) {
            console.log(`模块 ${moduleName} 已加载，跳过`);
            return Promise.resolve(this.loadedModules[moduleName]);
        }

        return new Promise((resolve, reject) => {
            try {
                console.log(`正在加载模块: ${moduleName} from ${modulePath}`);

                // 在抖音小游戏环境中，使用require加载模块
                if (typeof require !== 'undefined') {
                    const moduleExports = require(modulePath);

                    // 处理不同的导出格式
                    let actualClass = moduleExports;
                    if (moduleExports && typeof moduleExports === 'object') {
                        // 如果是对象导出，尝试获取同名属性
                        if (moduleExports[moduleName]) {
                            actualClass = moduleExports[moduleName];
                        } else if (moduleExports.default) {
                            actualClass = moduleExports.default;
                        }
                    }

                    this.loadedModules[moduleName] = actualClass;

                    // 将模块注册到全局作用域，确保类可以被访问
                    if (typeof global !== 'undefined') {
                        global[moduleName] = actualClass;
                    }
                    if (typeof globalThis !== 'undefined') {
                        globalThis[moduleName] = actualClass;
                    }
                    if (typeof window !== 'undefined') {
                        window[moduleName] = actualClass;
                    }

                    console.log(`模块 ${moduleName} 加载成功并注册到全局作用域`);
                    resolve(actualClass);
                } else {
                    // 备用方案：直接resolve，使用简化版本
                    console.warn(`无法加载模块 ${moduleName}，使用简化版本`);
                    resolve(null);
                }
            } catch (error) {
                console.error(`加载模块 ${moduleName} 失败:`, error);
                // 不抛出错误，而是resolve null，让游戏继续运行
                resolve(null);
            }
        });
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GameManager;
} else {
    window.GameManager = GameManager;
}