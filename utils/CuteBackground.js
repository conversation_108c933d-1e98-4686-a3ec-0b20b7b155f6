/**
 * 可爱风背景渲染类
 * 提供统一的可爱风背景效果，可在主页和游戏界面复用
 */
class CuteBackground {
    constructor(canvas, ctx, uiConfig) {
        this.canvas = canvas;
        this.ctx = ctx;
        this.uiConfig = uiConfig;
        
        // 动画时间
        this.animationTime = 0;
        
        // 背景元素
        this.backgroundElements = {
            stars: [],
            clouds: [],
            meteors: [],
            hearts: [],
            sparkles: [],
            bubbles: []
        };
        
        // 初始化背景元素
        this.initBackgroundElements();
    }
    
    // 初始化背景元素
    initBackgroundElements() {
        const bgConfig = this.getBackgroundConfig();
        if (!bgConfig) return;
        
        // 初始化星星
        if (bgConfig.stars && bgConfig.stars.enabled) {
            this.backgroundElements.stars = [];
            for (let i = 0; i < bgConfig.stars.count; i++) {
                this.backgroundElements.stars.push({
                    x: Math.random() * this.canvas.width,
                    y: Math.random() * this.canvas.height,
                    size: bgConfig.stars.minSize + Math.random() * (bgConfig.stars.maxSize - bgConfig.stars.minSize),
                    color: bgConfig.stars.colors[Math.floor(Math.random() * bgConfig.stars.colors.length)],
                    twinkle: Math.random() * Math.PI * 2,
                    twinkleSpeed: bgConfig.stars.twinkleSpeed + Math.random() * 0.5
                });
            }
        }
        
        // 初始化云朵
        if (bgConfig.clouds && bgConfig.clouds.enabled) {
            this.backgroundElements.clouds = [];
            for (let i = 0; i < bgConfig.clouds.count; i++) {
                this.backgroundElements.clouds.push({
                    x: Math.random() * this.canvas.width,
                    y: Math.random() * this.canvas.height * 0.6,
                    size: bgConfig.clouds.minSize + Math.random() * (bgConfig.clouds.maxSize - bgConfig.clouds.minSize),
                    speed: bgConfig.clouds.speed + Math.random() * 0.2,
                    opacity: bgConfig.clouds.opacity
                });
            }
        }
        
        // 初始化流星
        this.backgroundElements.meteors = [];
        this.lastMeteorTime = 0;
        
        // 初始化装饰元素（爱心、星星、泡泡）
        this.initDecorationElements(bgConfig);
    }
    
    // 初始化装饰元素
    initDecorationElements(bgConfig) {
        // 这里可以根据不同页面的需求初始化不同的装饰元素
        // 主页和游戏页面可以有不同的装饰配置
    }
    
    // 获取背景配置（子类需要实现）
    getBackgroundConfig() {
        // 默认返回主页背景配置
        return this.uiConfig ? this.uiConfig.getMainPageBackgroundConfig() : null;
    }
    
    // 更新动画
    update() {
        this.animationTime += 0.016; // 60fps
        this.updateBackgroundElements();
    }
    
    // 更新背景元素
    updateBackgroundElements() {
        const bgConfig = this.getBackgroundConfig();
        if (!bgConfig) return;
        
        // 更新星星闪烁
        this.backgroundElements.stars.forEach(star => {
            star.twinkle += star.twinkleSpeed * 0.016;
        });
        
        // 更新云朵位置
        this.backgroundElements.clouds.forEach(cloud => {
            cloud.x += cloud.speed;
            if (cloud.x > this.canvas.width + cloud.size) {
                cloud.x = -cloud.size;
                cloud.y = Math.random() * this.canvas.height * 0.6;
            }
        });
        
        // 更新流星
        this.backgroundElements.meteors = this.backgroundElements.meteors.filter(meteor => {
            meteor.x += meteor.vx;
            meteor.y += meteor.vy;
            meteor.life -= 0.016;
            return meteor.life > 0 && meteor.x < this.canvas.width + 100 && meteor.y < this.canvas.height + 100;
        });
        
        // 生成新流星
        if (bgConfig.meteors && bgConfig.meteors.enabled && 
            Date.now() - this.lastMeteorTime > bgConfig.meteors.spawnInterval) {
            if (this.backgroundElements.meteors.length < bgConfig.meteors.count) {
                this.createMeteor(bgConfig.meteors);
                this.lastMeteorTime = Date.now();
            }
        }
    }
    
    // 创建流星
    createMeteor(meteorConfig) {
        const startX = -50;
        const startY = Math.random() * this.canvas.height * 0.5;
        const angle = Math.PI / 6 + Math.random() * Math.PI / 6;
        
        let meteorColor;
        if (meteorConfig.colors && Array.isArray(meteorConfig.colors)) {
            meteorColor = meteorConfig.colors[Math.floor(Math.random() * meteorConfig.colors.length)];
        } else {
            meteorColor = meteorConfig.color || '#FFD700';
        }
        
        this.backgroundElements.meteors.push({
            x: startX,
            y: startY,
            vx: Math.cos(angle) * meteorConfig.speed,
            vy: Math.sin(angle) * meteorConfig.speed,
            life: 3,
            length: meteorConfig.length,
            color: meteorColor,
            glowColor: meteorConfig.glowColor
        });
    }
    
    // 渲染背景
    render() {
        const bgConfig = this.getBackgroundConfig();
        if (!bgConfig) return;
        
        this.ctx.save();
        
        // 渲染渐变背景
        if (bgConfig.gradient) {
            this.renderGradientBackground(bgConfig.gradient);
        }
        
        // 渲染背景元素
        this.renderBackgroundElements(bgConfig);
        
        this.ctx.restore();
    }
    
    // 渲染渐变背景
    renderGradientBackground(gradientConfig) {
        let gradient;
        if (gradientConfig.direction === 'radial') {
            gradient = this.ctx.createRadialGradient(
                this.canvas.width / 2, this.canvas.height / 2, 0,
                this.canvas.width / 2, this.canvas.height / 2,
                Math.max(this.canvas.width, this.canvas.height) / 2
            );
        } else {
            gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        }
        
        const colors = gradientConfig.colors;
        for (let i = 0; i < colors.length; i++) {
            gradient.addColorStop(i / (colors.length - 1), colors[i]);
        }
        
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }
    
    // 渲染背景元素
    renderBackgroundElements(bgConfig) {
        // 渲染云朵
        if (bgConfig.clouds && bgConfig.clouds.enabled) {
            this.renderClouds(bgConfig.clouds);
        }
        
        // 渲染星星
        if (bgConfig.stars && bgConfig.stars.enabled) {
            this.renderStars(bgConfig.stars);
        }
        
        // 渲染流星
        if (bgConfig.meteors && bgConfig.meteors.enabled) {
            this.renderMeteors(bgConfig.meteors);
        }
    }
    
    // 渲染云朵
    renderClouds(cloudConfig) {
        if (!this.backgroundElements.clouds) return;
        
        this.ctx.save();
        this.ctx.fillStyle = cloudConfig.color;
        this.ctx.globalAlpha = cloudConfig.opacity;
        
        this.backgroundElements.clouds.forEach(cloud => {
            this.renderCloud(cloud.x, cloud.y, cloud.size);
        });
        
        this.ctx.restore();
    }
    
    // 渲染单个云朵
    renderCloud(x, y, size) {
        this.ctx.save();
        this.ctx.translate(x, y);
        
        const circles = [
            { x: 0, y: 0, r: size * 0.5 },
            { x: -size * 0.3, y: -size * 0.1, r: size * 0.4 },
            { x: size * 0.3, y: -size * 0.1, r: size * 0.4 },
            { x: -size * 0.5, y: size * 0.1, r: size * 0.3 },
            { x: size * 0.5, y: size * 0.1, r: size * 0.3 }
        ];
        
        this.ctx.beginPath();
        circles.forEach(circle => {
            this.ctx.arc(circle.x, circle.y, circle.r, 0, Math.PI * 2);
        });
        this.ctx.fill();
        
        this.ctx.restore();
    }
    
    // 渲染星星
    renderStars(starConfig) {
        if (!this.backgroundElements.stars) return;
        
        this.backgroundElements.stars.forEach(star => {
            this.ctx.save();
            
            const twinkle = Math.sin(star.twinkle) * 0.5 + 0.5;
            this.ctx.globalAlpha = twinkle;
            
            this.ctx.fillStyle = star.color;
            this.ctx.shadowColor = star.color;
            this.ctx.shadowBlur = star.size * 2;
            
            this.renderStar(star.x, star.y, star.size);
            
            this.ctx.restore();
        });
    }
    
    // 渲染五角星
    renderStar(x, y, size) {
        this.ctx.save();
        this.ctx.translate(x, y);
        
        this.ctx.beginPath();
        for (let i = 0; i < 5; i++) {
            const angle = (i * 4 * Math.PI) / 5;
            const x1 = Math.cos(angle) * size;
            const y1 = Math.sin(angle) * size;
            
            if (i === 0) {
                this.ctx.moveTo(x1, y1);
            } else {
                this.ctx.lineTo(x1, y1);
            }
        }
        this.ctx.closePath();
        this.ctx.fill();
        
        this.ctx.restore();
    }
    
    // 渲染流星
    renderMeteors(meteorConfig) {
        if (!this.backgroundElements.meteors) return;
        
        this.backgroundElements.meteors.forEach(meteor => {
            this.ctx.save();
            
            const gradient = this.ctx.createLinearGradient(
                meteor.x, meteor.y,
                meteor.x - meteor.vx * meteor.length / meteorConfig.speed,
                meteor.y - meteor.vy * meteor.length / meteorConfig.speed
            );
            gradient.addColorStop(0, meteor.color);
            gradient.addColorStop(1, 'transparent');
            
            this.ctx.strokeStyle = gradient;
            this.ctx.lineWidth = 3;
            this.ctx.shadowColor = meteor.glowColor;
            this.ctx.shadowBlur = 10;
            
            this.ctx.beginPath();
            this.ctx.moveTo(meteor.x, meteor.y);
            this.ctx.lineTo(
                meteor.x - meteor.vx * meteor.length / meteorConfig.speed,
                meteor.y - meteor.vy * meteor.length / meteorConfig.speed
            );
            this.ctx.stroke();
            
            this.ctx.fillStyle = meteor.color;
            this.ctx.beginPath();
            this.ctx.arc(meteor.x, meteor.y, 2, 0, Math.PI * 2);
            this.ctx.fill();
            
            this.ctx.restore();
        });
    }
    
    // 销毁资源
    destroy() {
        this.backgroundElements = {
            stars: [],
            clouds: [],
            meteors: [],
            hearts: [],
            sparkles: [],
            bubbles: []
        };
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CuteBackground;
} else if (typeof window !== 'undefined') {
    window.CuteBackground = CuteBackground;
}
