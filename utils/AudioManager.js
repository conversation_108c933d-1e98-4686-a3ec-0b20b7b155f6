// 音频管理器
class AudioManager {
    constructor(uiConfig = null) {
        this.sounds = {};
        this.music = {};
        this.currentMusic = null;
        this.volume = 0.7;
        this.musicVolume = 0.5;
        this.soundVolume = 0.8;
        this.isMuted = false;
        this.uiConfig = uiConfig;

        // 初始化音频路径配置
        this.initAudioPaths();

        console.log('AudioManager初始化完成');
    }

    // 初始化音频路径配置
    initAudioPaths() {
        if (this.uiConfig) {
            // 从UI配置中获取音频路径
            const audioConfig = this.uiConfig.getAudioConfig();

            this.soundPaths = {
                'so': this.uiConfig.getAudioPath('so'),
                'shua': this.uiConfig.getAudioPath('shua'),
                'bomb': this.uiConfig.getAudioPath('bomb'),
                'wa': this.uiConfig.getAudioPath('wa'),
                'good': this.uiConfig.getAudioPath('good'),
                'cat': this.uiConfig.getAudioPath('cat'),
                'lose': this.uiConfig.getAudioPath('lose'),
                'win': this.uiConfig.getAudioPath('win')
            };

            this.musicPaths = {
                'background': this.uiConfig.getAudioPath('background')
            };
        } else {
            // 后备方案：使用硬编码路径
            this.soundPaths = {
                'so': 'audios/so.mp3',        // 普通消除音效
                'shua': 'audios/shua.mp3',    // 火箭消除音效
                'bomb': 'audios/bomb.mp3',    // 炸弹消除音效
                'wa': 'audios/wa.mp3',        // 2连击音效
                'good': 'audios/good.mp3',    // 4连击音效
                'cat': 'audios/cat.mp3',      // 猫咪音效
                'lose': 'audios/lose.mp3',    // 失败音效
                'win': 'audios/win.mp3'       // 胜利音效
            };

            this.musicPaths = {
                'background': 'audios/background.mp3'
            };
        }
    }
    
    // 预加载音频文件
    async preloadAudio() {
        console.log('开始预加载音频文件');
        
        try {
            // 预加载音效
            for (const [name, path] of Object.entries(this.soundPaths)) {
                await this.loadSound(name, path);
            }
            
            // 预加载音乐
            for (const [name, path] of Object.entries(this.musicPaths)) {
                await this.loadMusic(name, path);
            }
            
            console.log('音频文件预加载完成');
        } catch (error) {
            console.warn('音频文件预加载失败:', error);
        }
    }
    
    // 加载音效
    loadSound(name, path) {
        return new Promise((resolve, reject) => {
            const audio = new Audio();
            audio.preload = 'auto';
            
            audio.oncanplaythrough = () => {
                this.sounds[name] = audio;
                console.log(`音效 ${name} 加载成功`);
                resolve();
            };
            
            audio.onerror = () => {
                console.warn(`音效 ${name} 加载失败: ${path}`);
                resolve(); // 不阻塞其他音频加载
            };
            
            audio.src = path;
        });
    }
    
    // 加载音乐
    loadMusic(name, path) {
        return new Promise((resolve, reject) => {
            const audio = new Audio();
            audio.preload = 'auto';
            audio.loop = true;
            
            audio.oncanplaythrough = () => {
                this.music[name] = audio;
                console.log(`音乐 ${name} 加载成功`);
                resolve();
            };
            
            audio.onerror = () => {
                console.warn(`音乐 ${name} 加载失败: ${path}`);
                resolve(); // 不阻塞其他音频加载
            };
            
            audio.src = path;
        });
    }
    
    // 播放音效
    playSound(name) {
        if (this.isMuted) return;
        
        const sound = this.sounds[name];
        if (sound) {
            try {
                sound.currentTime = 0;
                sound.volume = this.soundVolume * this.volume;
                sound.play().catch(error => {
                    console.warn(`播放音效 ${name} 失败:`, error);
                });
            } catch (error) {
                console.warn(`播放音效 ${name} 失败:`, error);
            }
        } else {
            console.warn(`音效 ${name} 未找到`);
        }
    }
    
    // 播放背景音乐
    playMusic(name) {
        if (this.isMuted) return;
        
        // 停止当前音乐
        this.stopMusic();
        
        const music = this.music[name];
        if (music) {
            try {
                music.currentTime = 0;
                music.volume = this.musicVolume * this.volume;
                music.play().then(() => {
                    this.currentMusic = music;
                    console.log(`开始播放音乐: ${name}`);
                }).catch(error => {
                    console.warn(`播放音乐 ${name} 失败:`, error);
                });
            } catch (error) {
                console.warn(`播放音乐 ${name} 失败:`, error);
            }
        } else {
            console.warn(`音乐 ${name} 未找到`);
        }
    }
    
    // 停止背景音乐
    stopMusic() {
        if (this.currentMusic) {
            try {
                this.currentMusic.pause();
                this.currentMusic.currentTime = 0;
                this.currentMusic = null;
                console.log('背景音乐已停止');
            } catch (error) {
                console.warn('停止音乐失败:', error);
            }
        }
    }
    
    // 暂停背景音乐
    pauseMusic() {
        if (this.currentMusic) {
            try {
                this.currentMusic.pause();
                console.log('背景音乐已暂停');
            } catch (error) {
                console.warn('暂停音乐失败:', error);
            }
        }
    }
    
    // 恢复背景音乐
    resumeMusic() {
        if (this.currentMusic && this.currentMusic.paused && !this.isMuted) {
            try {
                this.currentMusic.play().catch(error => {
                    console.warn('恢复音乐失败:', error);
                });
                console.log('背景音乐已恢复');
            } catch (error) {
                console.warn('恢复音乐失败:', error);
            }
        }
    }
    
    // 设置总音量
    setVolume(volume) {
        this.volume = Math.max(0, Math.min(1, volume));
        
        // 更新当前播放的音乐音量
        if (this.currentMusic) {
            this.currentMusic.volume = this.musicVolume * this.volume;
        }
        
        console.log(`音量设置为: ${Math.round(this.volume * 100)}%`);
    }
    
    // 设置音效音量
    setSoundVolume(volume) {
        this.soundVolume = Math.max(0, Math.min(1, volume));
        console.log(`音效音量设置为: ${Math.round(this.soundVolume * 100)}%`);
    }
    
    // 设置音乐音量
    setMusicVolume(volume) {
        this.musicVolume = Math.max(0, Math.min(1, volume));
        
        // 更新当前播放的音乐音量
        if (this.currentMusic) {
            this.currentMusic.volume = this.musicVolume * this.volume;
        }
        
        console.log(`音乐音量设置为: ${Math.round(this.musicVolume * 100)}%`);
    }
    
    // 静音/取消静音
    toggleMute() {
        this.isMuted = !this.isMuted;
        
        if (this.isMuted) {
            this.pauseMusic();
        } else {
            this.resumeMusic();
        }
        
        console.log(`音频${this.isMuted ? '已静音' : '已取消静音'}`);
        return this.isMuted;
    }
    
    // 获取音频状态
    getAudioState() {
        return {
            volume: this.volume,
            soundVolume: this.soundVolume,
            musicVolume: this.musicVolume,
            isMuted: this.isMuted,
            isPlayingMusic: !!this.currentMusic && !this.currentMusic.paused,
            loadedSounds: Object.keys(this.sounds),
            loadedMusic: Object.keys(this.music)
        };
    }
    
    // 清理资源
    destroy() {
        console.log('开始清理AudioManager资源');
        
        // 停止所有音频
        this.stopMusic();
        
        // 清理音效
        Object.values(this.sounds).forEach(sound => {
            try {
                sound.pause();
                sound.src = '';
            } catch (error) {
                console.warn('清理音效失败:', error);
            }
        });
        
        // 清理音乐
        Object.values(this.music).forEach(music => {
            try {
                music.pause();
                music.src = '';
            } catch (error) {
                console.warn('清理音乐失败:', error);
            }
        });
        
        this.sounds = {};
        this.music = {};
        this.currentMusic = null;
        
        console.log('AudioManager资源清理完成');
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AudioManager;
} else {
    window.AudioManager = AudioManager;
}
