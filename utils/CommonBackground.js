/**
 * 公共背景渲染类 - 为所有页面提供统一的可爱背景效果
 * 包含渐变背景、装饰元素、动画效果等
 */
class CommonBackground {
    constructor(canvas, ctx) {
        this.canvas = canvas;
        this.ctx = ctx;
        
        // 背景动画时间
        this.animationTime = 0;
        
        // 背景星星效果
        this.backgroundStars = null;
        
        // 装饰元素配置
        this.decorations = {
            clouds: [
                { x: canvas.width * 0.15, y: canvas.height * 0.2, size: 40, speed: 0.3 },
                { x: canvas.width * 0.85, y: canvas.height * 0.15, size: 35, speed: 0.2 },
                { x: canvas.width * 0.1, y: canvas.height * 0.8, size: 45, speed: 0.25 },
                { x: canvas.width * 0.9, y: canvas.height * 0.75, size: 38, speed: 0.35 }
            ],
            hearts: [
                { x: canvas.width * 0.25, y: canvas.height * 0.4, size: 20, speed: 0.4 },
                { x: canvas.width * 0.75, y: canvas.height * 0.35, size: 15, speed: 0.3 },
                { x: canvas.width * 0.2, y: canvas.height * 0.65, size: 18, speed: 0.5 }
            ],
            stars: [
                { x: canvas.width * 0.3, y: canvas.height * 0.25, size: 12, speed: 0.6 },
                { x: canvas.width * 0.7, y: canvas.height * 0.6, size: 10, speed: 0.4 },
                { x: canvas.width * 0.5, y: canvas.height * 0.85, size: 14, speed: 0.5 }
            ]
        };
        
        this.initBackgroundEffects();
    }
    
    // 初始化背景效果
    initBackgroundEffects() {
        try {
            if (typeof BackgroundUtils !== 'undefined') {
                this.backgroundStars = BackgroundUtils.createStars(15, this.canvas.width, this.canvas.height);
            }
        } catch (error) {
            console.warn('背景星星效果初始化失败:', error);
            this.backgroundStars = null;
        }
    }
    
    // 更新背景动画
    update() {
        this.animationTime += 0.02;
        
        // 更新背景星星
        if (this.backgroundStars && typeof BackgroundUtils !== 'undefined') {
            try {
                BackgroundUtils.updateStars(this.backgroundStars);
            } catch (error) {
                console.warn('背景星星更新失败:', error);
            }
        }
        
        // 更新装饰元素位置（轻微浮动效果）
        this.decorations.clouds.forEach(cloud => {
            cloud.offsetY = Math.sin(this.animationTime * cloud.speed) * 3;
        });
        
        this.decorations.hearts.forEach(heart => {
            heart.offsetY = Math.sin(this.animationTime * heart.speed + 1) * 2;
            heart.offsetX = Math.cos(this.animationTime * heart.speed * 0.5) * 1;
        });
        
        this.decorations.stars.forEach(star => {
            star.offsetY = Math.sin(this.animationTime * star.speed + 2) * 1.5;
            star.rotation = this.animationTime * star.speed * 0.5;
        });
    }
    
    // 渲染背景
    render(style = 'cute') {
        // 清空画布
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 根据样式渲染不同的背景
        switch (style) {
            case 'cute':
                this.renderCuteBackground();
                break;
            case 'main':
                this.renderMainBackground();
                break;
            case 'game':
                this.renderGameBackground();
                break;
            case 'simple':
                this.renderSimpleBackground();
                break;
            default:
                this.renderCuteBackground();
        }
        
        // 渲染装饰元素
        this.renderDecorations();
        
        // 渲染背景星星
        this.renderBackgroundStars();
    }
    
    // 渲染可爱风格背景（默认）
    renderCuteBackground() {
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        gradient.addColorStop(0, '#FFE5F1');  // 浅粉色
        gradient.addColorStop(0.3, '#FFB6D9'); // 粉色
        gradient.addColorStop(0.6, '#D4A5FF'); // 薄荷紫
        gradient.addColorStop(1, '#A8E6CF');   // 薄荷绿
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }
    
    // 渲染主页背景
    renderMainBackground() {
        const gradient = this.ctx.createRadialGradient(
            this.canvas.width / 2, this.canvas.height / 2, 0,
            this.canvas.width / 2, this.canvas.height / 2, Math.max(this.canvas.width, this.canvas.height)
        );
        gradient.addColorStop(0, '#FFE5F1');
        gradient.addColorStop(0.4, '#FFB6D9');
        gradient.addColorStop(0.7, '#E6D7FF');
        gradient.addColorStop(1, '#B8E6FF');
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }
    
    // 渲染游戏背景
    renderGameBackground() {
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        gradient.addColorStop(0, '#F0F8FF');  // 爱丽丝蓝
        gradient.addColorStop(0.3, '#E6E6FA'); // 薰衣草
        gradient.addColorStop(0.6, '#FFE4E1'); // 迷雾玫瑰
        gradient.addColorStop(1, '#F0FFF0');   // 蜜瓜色
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }
    
    // 渲染简单背景
    renderSimpleBackground() {
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        gradient.addColorStop(0, '#F8F9FA');
        gradient.addColorStop(1, '#E9ECEF');
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }
    
    // 渲染装饰元素
    renderDecorations() {
        this.ctx.save();
        
        // 绘制云朵
        this.ctx.globalAlpha = 0.3;
        this.decorations.clouds.forEach(cloud => {
            const y = cloud.y + (cloud.offsetY || 0);
            this.drawCloud(cloud.x, y, cloud.size);
        });
        
        // 绘制心形
        this.ctx.globalAlpha = 0.2;
        this.decorations.hearts.forEach(heart => {
            const x = heart.x + (heart.offsetX || 0);
            const y = heart.y + (heart.offsetY || 0);
            this.drawHeart(x, y, heart.size);
        });
        
        // 绘制星星
        this.ctx.globalAlpha = 0.25;
        this.decorations.stars.forEach(star => {
            const x = star.x;
            const y = star.y + (star.offsetY || 0);
            const rotation = star.rotation || 0;
            this.drawStar(x, y, star.size, rotation);
        });
        
        this.ctx.restore();
    }
    
    // 绘制云朵
    drawCloud(x, y, size) {
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.beginPath();
        this.ctx.arc(x, y, size * 0.5, 0, Math.PI * 2);
        this.ctx.arc(x + size * 0.3, y, size * 0.4, 0, Math.PI * 2);
        this.ctx.arc(x - size * 0.3, y, size * 0.4, 0, Math.PI * 2);
        this.ctx.arc(x + size * 0.15, y - size * 0.3, size * 0.35, 0, Math.PI * 2);
        this.ctx.arc(x - size * 0.15, y - size * 0.3, size * 0.35, 0, Math.PI * 2);
        this.ctx.fill();
    }
    
    // 绘制心形
    drawHeart(x, y, size) {
        this.ctx.fillStyle = '#FFB6D9';
        this.ctx.beginPath();
        this.ctx.moveTo(x, y + size * 0.3);
        this.ctx.bezierCurveTo(x, y, x - size * 0.5, y, x - size * 0.5, y + size * 0.3);
        this.ctx.bezierCurveTo(x - size * 0.5, y + size * 0.6, x, y + size * 0.9, x, y + size);
        this.ctx.bezierCurveTo(x, y + size * 0.9, x + size * 0.5, y + size * 0.6, x + size * 0.5, y + size * 0.3);
        this.ctx.bezierCurveTo(x + size * 0.5, y, x, y, x, y + size * 0.3);
        this.ctx.fill();
    }
    
    // 绘制星星
    drawStar(x, y, size, rotation = 0) {
        this.ctx.save();
        this.ctx.translate(x, y);
        this.ctx.rotate(rotation);
        
        this.ctx.fillStyle = '#FFD700';
        this.ctx.beginPath();
        
        const spikes = 5;
        const outerRadius = size;
        const innerRadius = size * 0.4;
        
        for (let i = 0; i < spikes * 2; i++) {
            const radius = i % 2 === 0 ? outerRadius : innerRadius;
            const angle = (i * Math.PI) / spikes;
            const px = Math.cos(angle) * radius;
            const py = Math.sin(angle) * radius;
            
            if (i === 0) {
                this.ctx.moveTo(px, py);
            } else {
                this.ctx.lineTo(px, py);
            }
        }
        
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.restore();
    }
    
    // 渲染背景星星
    renderBackgroundStars() {
        if (this.backgroundStars && typeof BackgroundUtils !== 'undefined') {
            try {
                BackgroundUtils.renderStars(this.ctx, this.backgroundStars);
            } catch (error) {
                console.warn('背景星星渲染失败:', error);
            }
        }
    }
    
    // 添加粒子效果
    addParticleEffect(x, y, color = '#FFB6D9', count = 8) {
        const particles = [];
        for (let i = 0; i < count; i++) {
            particles.push({
                x: x,
                y: y,
                vx: (Math.random() - 0.5) * 6,
                vy: (Math.random() - 0.5) * 6,
                life: 1.0,
                decay: 0.02,
                size: Math.random() * 3 + 2,
                color: color
            });
        }
        return particles;
    }
    
    // 渲染粒子效果
    renderParticles(particles) {
        if (!particles || !Array.isArray(particles)) return;
        
        particles.forEach(particle => {
            this.ctx.save();
            this.ctx.globalAlpha = particle.life;
            this.ctx.fillStyle = particle.color;
            this.ctx.beginPath();
            this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            this.ctx.fill();
            this.ctx.restore();
        });
    }
    
    // 更新粒子效果
    updateParticles(particles) {
        if (!particles || !Array.isArray(particles)) return [];
        
        return particles.filter(particle => {
            particle.x += particle.vx;
            particle.y += particle.vy;
            particle.life -= particle.decay;
            particle.vx *= 0.98;
            particle.vy *= 0.98;
            return particle.life > 0;
        });
    }
    
    // 销毁背景资源
    destroy() {
        this.backgroundStars = null;
        this.decorations = null;
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CommonBackground;
} else {
    window.CommonBackground = CommonBackground;
}